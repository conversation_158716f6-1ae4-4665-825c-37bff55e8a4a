import { httpChe<PERSON>Health, 
    httpUpdateAgent, 
    httpUpdateStatus, 
    httpGetProject, 
    httpSay, 
    httpGetExtensionWithdraw, 
    httpMarkExtensionWithdrawDone,
    httpFinishServerRequest } from "./request.js";
import { LighterPlatform } from "./platforms/lighter/lighter.js";
import { AsterPlatform } from "./platforms/aster/aster.js";
import { version } from "./version.js";

export const STATUS = {
    UNINITIALIZED: "uninitialized",
    INITIALIZED: "initialized",
    RUNNING: "running",
    STOPPED: "stopped",
};

const STOPLOSS_ENABLED = true;

const MASTER = "master";
const COPIER = "copier";

function randomRange(min, max) {
    return Math.random() * (max - min) + min;
}

function randomLogID() {
    return Math.floor(randomRange(1000, 9999));
}

function parseISOTime(serverTimeString) {
    if (!serverTimeString || serverTimeString === "0001-01-01T00:00:00Z") {
        return 0;
    }
    return new Date(serverTimeString).getTime();
}

let refid = randomLogID();

// debug log function
function log(...args) {
    let now = new Date().toISOString();
    console.log(`#${refid} ${now}: `, ...args);
}

function logError(...args) {
    let now = new Date().toISOString();
    console.error(`#${refid} ${now}: `, ...args);
}

function isMarginCall(agent) {
    let threshold = 0.03;
    let positionValue = 0.0;
    for (const position of agent.positions) {
        positionValue += position.size * position.markPrice;
    }
    // if positionValue is 0, it means the agent has no position, so it's not margin call
    if (positionValue == 0) {
        return false;
    }
    // totalMargin is 0 means the agent has no margin, so it's not margin call
    // some bad data may cause totalMargin is 0, so we need to check it
    if (agent.totalMargin == 0) {
        return false;
    }
    let marginRatio = agent.totalMargin / positionValue;
    log("[app] [master] check margin call, marginRatio:", marginRatio, ", positionValue:", positionValue, ", totalMargin:", agent.totalMargin, ", threshold:", threshold);
    if (marginRatio < threshold) {
        log("[app] [master] margin call, marginRatio:", marginRatio, ", threshold:", threshold);
        return true;
    }
    return false;
}

function reverseSide(side) {
    return side == "long" ? "short" : "long";
}

class Executor {
    constructor(data) {
        this.role = data.role;
        this.settings = data.settings;
        this.platform = data.platform;
        this.app = data.app;
        this.agents = [];
        this.lastTradeTime = 0;
        this.coolingTime = 0;
        this.lastPositionSize = {}; // only used in case of master in manual mode
    }

    setAgents(agents) {
        this.agents = agents;

        // check if the position size is changed
        // if changed, set lastTradeTime to prevent reduplicate trade
        // 
        // when master agent in manual mode, we use tp/sl orders to close position
        // if the position is closed by tp/sl, the copier agent may still shows master positions not closed, when there is a delay
        // so we use the position size change to detect the position is closed by tp/sl
        // and set lastTradeTime to prevent reduplicate trade, after that we wait for the master agent positions to in sync
        if (this.masterAgent()?.settings?.manualMode) {
            let currentPositionSize = {};
            for (const pos in this.currentAgent().positions) {
                currentPositionSize[pos.symbol] = pos.size;
            }
            // check if the position size is changed
            // if the position changed by executor itself, the lastTradeTime would be very close
            // in such case, the lastTradeTime is already set, we don't need to try to change lastTradeTime
            let notRecentlyTraded = Date.now() - this.lastTradeTime > 1000*3;
            if (notRecentlyTraded) {
                for (const symbol in currentPositionSize) {
                    const currentSize = currentPositionSize[symbol] || 0;
                    const lastSize = this.lastPositionSize?.[symbol] || 0;
                    if (lastSize == 0 && currentSize > 0) {
                        log("[app] [executor] position opened for " + symbol + ":", lastSize, "->", currentSize);
                        this.lastTradeTime = Date.now();
                    }
                    if (currentSize !== lastSize) {
                        log("[app] [executor] position size changed for " + symbol + ":", lastSize, "->", currentSize);
                    }
                }
                // Check for positions that were closed (exist in last but not in current)
                for (const symbol in this.lastPositionSize) {
                    if (!(symbol in currentPositionSize)) {
                        log("[app] [executor] position closed for " + symbol + ":", this.lastPositionSize[symbol], "-> 0");
                        this.lastTradeTime = Date.now();
                    }
                }
            }
            this.lastPositionSize = currentPositionSize;
        }
    }

    setSettings(settings) {
        this.settings = settings;
    }

    setPlatform(platform) {
        this.platform = platform;
    }

    currentAgent() {
        if (!this.platform) {
            return null;
        }
        return this.agents?.find((agent) => agent.userId === this.settings.userId);
    }

    masterAgent() {
        if (!this.platform) {
            return null;
        }
        return this.agents.find((agent) => agent.role === MASTER);
    }

    async do() {
        // Base implementation - can be overridden by subclasses
        throw new Error("do() method must be implemented by subclass");
    }

    async closePositions() {
        log(`[${this.role}] closePositions called, currentAgent:`, this.currentAgent());
        if (!this.currentAgent()) {
            logError(`[${this.role}] currentAgent is undefined in closePositions`);
            return;
        }
        let positions = await this.currentAgent()?.positions;
        log(`[${this.role}] closing positions:`, positions);
        if (!positions) {
            logError(`[${this.role}] positions is undefined in closePositions`);
            return;
        }
        for (const position of positions) {
            await this._closePosition(position);
        }
    }

    // market open position
    async _openPosition(symbol, side, size) {
        log(`[app] [${this.role}] opening position:`, symbol, side, size);
        size = Math.abs(size);
        return this._createMarketOrder(symbol, side, size, false);
    }

    // market close position
    async _closePosition(position) {
        log(`[app] [${this.role}] closing position:`, position);
        let side = reverseSide(position.side);
        let size = Math.abs(position.size);
        await this._createMarketOrder(position.symbol, side, size, true);
    }

    async _createMarketOrderWithLimitOrder(symbol, side, size, reduceOnly = true) {
        log(`[app] [${this.role}] creating market order with limit order:`, symbol, side, size, reduceOnly);
        if (size == 0) {
            logError(`[app] [${this.role}] size is 0, skip create market order`);
            return;
        }
        let agent = await this.currentAgent();
        if (!agent) {
            logError(`[app] [${this.role}] current agent not found`);
            return;
        }
        let lastPrice = agent.lastPrice;
        let limitPrice = this.platform.CalculateOrderPrice(symbol, side, lastPrice);
        this.platform.CreateOrder({
            symbol: symbol,
            side: side,
            size: size,
            price: limitPrice,
            reduceOnly: reduceOnly,
        });
    }

    async _createMarketOrder(symbol, side, size, reduceOnly = true) {
        log(`[app] [${this.role}] creating market order:`, symbol, side, size, reduceOnly);
        if (size == 0) {
            logError(`[app] [${this.role}] size is 0, skip create market order`);
            return;
        }
        let agent = await this.currentAgent();
        if (!agent) {
            logError(`[app] [${this.role}] current agent not found`);
            return;
        }
        this.platform.CreateMarketOrder({
            symbol: symbol,
            side: side,
            size: size,
            reduceOnly: reduceOnly,
        });
    }

    roundSize(size, roundUp = false) {
        if (roundUp) {
            // size is float, get digits
            let digits = this.platform.getAmountDigits(this.settings.defaultSymbol);
            let newSize = Math.ceil(size * Math.pow(10, digits)) / Math.pow(10, digits);
            return newSize.toFixed(digits);
        }
        // size is float, get digits
        let digits = this.platform.getAmountDigits(this.settings.defaultSymbol);
        let newSize = Math.floor(size * Math.pow(10, digits)) / Math.pow(10, digits);
        return newSize.toFixed(digits);
    }
}

class Master extends Executor {
    constructor(data) {
        super({ ...data, role: MASTER });
        this.coolingTime = this.setCooling(false);
    }

    setCooling(forClosePosition = false) {
        // if coolingHour is negative, use exact hour mode
        // if coolingHour is positive, use interval mode
        if (this.settings.coolingHour < 0) {
            return this.setExactHourCooling(forClosePosition);
        }
        return this.setIntervalCooling(forClosePosition);
    }

    setExactHourCooling(forClosePosition = false) {
        let agent = this.currentAgent();
        let hasPosition = false;
        if (agent) {
            hasPosition = agent.positions.length > 0;
        }

        // Get current time
        const now = new Date();
        const currentHour = now.getHours();
        const currentMinutes = now.getMinutes();
        const absCoolingHour = Math.abs(this.settings.coolingHour);
        const baseDelta = Math.floor(absCoolingHour * 60) || 15; // Convert hours to minutes, default to 15
        
        // Add randomness to the delta
        const randomDelta = this.randomMinute(baseDelta);
        const delta = Math.floor(randomDelta / (1000 * 60)); // Convert milliseconds to minutes

        // Calculate next cooling time
        let nextCoolingTime = new Date(now);
        
        if (forClosePosition) {
            // If we have a position, always set cooling to next hour's XX:delta
            // This ensures we don't close positions before the hour transition
            nextCoolingTime.setHours(currentHour + 1, delta, 0, 0);
        } else {
            // If we don't have a position, set cooling until XX:(60-delta)
            const openTime = 60 - delta;
            if (currentMinutes < openTime) {
                // If current time is before XX:(60-delta), set to current hour's XX:(60-delta)
                nextCoolingTime.setMinutes(openTime, 0, 0);
            } else {
                // If current time is after XX:(60-delta), set to next hour's XX:(60-delta)
                nextCoolingTime.setHours(currentHour + 1, openTime, 0, 0);
            }
        }

        // Ensure cooling time is in the future
        if (nextCoolingTime <= now) {
            nextCoolingTime.setHours(nextCoolingTime.getHours() + 1);
        }

        this.coolingTime = nextCoolingTime.getTime();
        log("[app] [master] set exact hour cooling time:", new Date(this.coolingTime).toISOString(), 
            ", forClosePosition:", forClosePosition,
            ", baseDelta:", baseDelta,
            ", randomDelta:", randomDelta,
            ", finalDelta:", delta);
        return this.coolingTime;
    }

    setIntervalCooling(forClosePosition = false) {
        // default without position, cool time is half of interval hour
        log("[app] [master] set cooling time, coolingHour:", this.settings.coolingHour, ", forClosePosition:", forClosePosition);
        const coolingHour = parseFloat(this.settings.coolingHour);
        let maxMinutes = Math.floor(coolingHour * 60 * 0.5)+1;
        let cooling = 1000*60*maxMinutes;
        let randomCooling = this.randomMinute(maxMinutes);
        if (forClosePosition) {
            // with position, cool time is full interval hour
            maxMinutes = Math.floor(coolingHour * 60)+1;
            randomCooling = this.randomMinute(maxMinutes);
        }
        if (!Number.isNaN(randomCooling)) {
            cooling = randomCooling;
        } else {
            logError("[app] [master] randomCooling is NaN, cooling:", cooling, ", forClosePosition:", forClosePosition, ", now:", Date.now(), ", coolingTime:", this.coolingTime);
        }
        // if cooling time is less than tradeWaitingTime, set cooling time to tradeWaitingTime
        // prevent cooling time too short, which may cause no time to do trade
        let tradeWaitingTime = this.platform?.GetTradeWaitingTime() || 1000*10;
        if (cooling >= tradeWaitingTime) {   
            this.coolingTime = Date.now() + cooling;
            log("[app] [master] set cooling time, cooling:", cooling, ", forClosePosition:", forClosePosition, ", now:", Date.now(), ", coolingTime:", this.coolingTime);
        } else {
            this.coolingTime = Date.now() + this.platform.GetTradeWaitingTime();
            logError("[app] [master] cooling time is less than tradeWaitingTime, cooling:", cooling, ", forClosePosition:", forClosePosition, ", now:", Date.now(), ", coolingTime:", this.coolingTime);
        }
        return this.coolingTime;
    }

    isCooling() {
        return Date.now() < this.coolingTime;
    }

    // random time between 0.5 and 1.0 of the given minutes
    randomMinute(minutes) {
        let min = 0.5;
        let max = 1.0;
        let result = 1000;
        let random = Math.random() * (max - min) + min; // random between 0.5 and 1.0
        if (this.settings.debug) {
            result = Math.floor(1000 * minutes * random);
        } else {
            result = Math.floor(1000 * 60 * minutes * random);
        }
        log("[app] [master] random minute, result:", result, ", debug:", this.settings.debug, ", minutes:", minutes);
        return result;
    }

    async do() {
        // Master-specific implementation
        let currentAgent = this.currentAgent();
        if (!currentAgent) {
            logError("[app] [master] current agent not found");
            return this.lastTradeTime;
        }

        // if last trade time is less than tradeWaitingTime, skip the loop
        if (Date.now() - this.lastTradeTime < this.platform.GetTradeWaitingTime()) {
            log("[app] [master] last trade time is less than tradeWaitingTime, skip the loop");
            return this.lastTradeTime;
        }

        let positions = currentAgent.positions;
        log("[app] [master] current agent positions:", positions);

        if (positions.length === 0) {
            if (!this.isCooling()) {
                // check total margin < minMargin, stop open position
                let margin = currentAgent.totalMargin;
                if (margin < this.settings.minMargin) {
                    log("[app] [master] total margin < min margin, stop open position", margin);
                    // sent alert in backend, not in frontend
                    return this.lastTradeTime;
                }
                let randomSide = Math.random() < this.settings.longRatio ? "long" : "short";
                let size = parseFloat(this.settings.defaultSize);
                size = randomRange(0.6, 1) * size;
                let roundedSize = this.roundSize(size, true);
                log("[app] [master] open position, symbol:", this.settings.defaultSymbol, "side:", randomSide, "size:", size, "rounded size:", roundedSize);
                await this._openPosition(this.settings.defaultSymbol, randomSide, roundedSize);
                this.setCooling(true);
                this.lastTradeTime = Date.now();
            }
        } else {
            //  get one of the position's create time
            if (!this.isCooling()) {
                await this.closePositions();
                this.setCooling(false);
                this.lastTradeTime = Date.now();
            }
            // check if any position is margin call
            if (!this.settings.manualMode && isMarginCall(currentAgent)) {
                log("[app] [master] margin call, closing all positions");
                await this.closePositions();
                this.setCooling(false);
                this.lastTradeTime = Date.now();
                await this.app.say(`account ${this.settings.alias} margin call, closing all positions`);
            }
        }
        log("[app] [master] do end, lastTradeTime:", this.lastTradeTime);
        return this.lastTradeTime;
    }
}

class Copier extends Executor {
    constructor(data) {
        super({ ...data, role: COPIER, stats: {
            allTimePoints: 0,
            allTimeVolume: 0,
            allTimeProfit: 0,
            createTime: new Date().toISOString(),
        } });
    }

    setCooling() {
        log("[app] [copier] no need to set cooling");
    }

    async _syncPositionDiff(masterPosition, currentPosition) {
        // check if the same side, close current position if so, copy position next loop
        if (currentPosition.side == masterPosition.side) {
            log("[app] [copier] sync position diff, same side, close current position", currentPosition);
            await this._closePosition(currentPosition);
            return;
        }
        if (masterPosition.size == currentPosition.size) {
            log("[app] [copier] sync position diff, same size, no need to copy", currentPosition);
            return;
        }
        // calculate size diff between current position and master position
        let sizeDiff = (masterPosition.size * this.settings.copyPercentage) / 100 - currentPosition.size;
        if (sizeDiff == 0) {
            log("[app] [copier] sync position diff, same size, no need to copy", currentPosition);
            return;
        }

        log("[app] [copier] sync position diff, different side, copy position, masterPosition:", masterPosition, "currentPosition:", currentPosition);
        // if size diff is negative, reverse side
        // long: master 1.0, current 0.5 -> side = long 0.5; master 0.5, current 1.0 (sizeDiff < 0) -> side = short 0.5
        // short: master 1.0, current 0.5 -> side = short 0.5; master 0.5, current 1.0 (sizeDiff < 0) -> side = long 0.5
        let side = currentPosition.side;
        if (sizeDiff < 0) {
            side = reverseSide(side);
        }
        let roundedSize = this.roundSize(Math.abs(sizeDiff), false);
        log("[app] [copier] sync position diff, different side, sizeDiff:", sizeDiff, ", rounded size:", roundedSize);
        await this._openPosition(masterPosition.symbol, side, roundedSize);
    }

    // master position syncing delay and auto copy in manual mode can have race condition
    // so we use lastTradeTime to control the loop
    // in manual mode, the lastTradeTime is set to the time when the position is closed by tp/sl
    // the lastTradeTime is set in setAgents() in executor code
    // there is an alternative way to control the loop, use openOrdersCount, but it's not reliable
    async do() {
        log("[app] [copier] do start");
        let currentAgent = this.currentAgent();
        if (!currentAgent) {
            logError("[app] [copier] current agent not found");
            return this.lastTradeTime;
        }

        // if last trade time is less than tradeWaitingTime, skip the loop
        if (Date.now() - this.lastTradeTime < this.platform.GetTradeWaitingTime()) {
            log("[app][copier] last trade time is less than tradeWaitingTime, skip the loop");
            return this.lastTradeTime;
        }

        // if app is not running, close all positions
        // eliminate the risk of open position when app is not running
        // this risk will happen in such situation:
        // 1. copier received stop signal and close its position
        // 2. update projects shows there is a position in master agent
        // 3. copier open the position again
        // 4. update projects shows there is no position in master agent
        // 5. copier close its position the second time
        if (this.app.status != STATUS.RUNNING) {
            log("[app] [copier] app is not running, close all positions");
            await this.closePositions();
            this.lastTradeTime = Date.now();
            return this.lastTradeTime;
        }

        let currentPositions = currentAgent.positions || [];
        log("[app] [copier] current agent positions:", currentPositions);

        // Copier-specific implementation
        let masterAgent = this.masterAgent();
        if (!masterAgent) {
            logError("[app] [copier] master agent not found");
            return this.lastTradeTime;
        }
        let masterPositions = await masterAgent.positions;
        if (masterPositions.length === 0) {
            if (currentPositions.length > 0) {
                log("[app] [copier] master agent has no positions, close all positions");
                await this.closePositions();
                this.lastTradeTime = Date.now();
            } else {
                log("[app] [copier] master agent has no positions, copier has no positions, do nothing");
            }
            log("[app] [copier] do end, without master positions, lastTradeTime:", this.lastTradeTime);
            return this.lastTradeTime;
        }
        log("[app] [copier] master agent positions:", masterPositions);

        // copy all the positions from master agent to copier agent
        for (const masterPosition of masterPositions) {
            // if masterPosition expired, don't copy it
            let posCreateTime = masterPosition.createTime;
            let posCreateDate = new Date(posCreateTime);
            let posExpiredTime = posCreateDate.getTime() + 1000 * 60 * 5; // 5 minutes
            let nowTime = Date.now();
            if (nowTime > posExpiredTime) {
                log("[app] [copier] master position expired, don't copy it, posCreateTime:", posCreateDate.toISOString(), ", posExpiredTime:", new Date(posExpiredTime).toISOString(), ", nowTime:", new Date(nowTime).toISOString());
                continue;
            }
            let currentPosition = currentPositions.find((p) => p.symbol === masterPosition.symbol);
            // if currently has position, check qty difference
            if (currentPosition) {
                await this._syncPositionDiff(masterPosition, currentPosition);
                this.lastTradeTime = Date.now();
            } else {
                let side = reverseSide(masterPosition.side);
                let size = (masterPosition.size * this.settings.copyPercentage) / 100;
                let roundedSize = this.roundSize(size, true);
                if (roundedSize > masterPosition.size) {
                    log("[app] [copier] rounded size is greater than master position size, use master position size, rounded size:", roundedSize, ", size:", size);
                    roundedSize = masterPosition.size;
                }
                log("[app] [copier] copy position, masterPosition:", masterPosition, "side:", side, ", size:", size, ", rounded size:", roundedSize);
                await this._openPosition(masterPosition.symbol, side, roundedSize);
                this.lastTradeTime = Date.now();
            }
        }
        log("[app] [copier] do end, with master positions, lastTradeTime:", this.lastTradeTime);
        return this.lastTradeTime;
    }
}

const PROJECT_STATUS = {
    RUNNING: "running",
    STOPPED: "stopped",
};

export class CopierApp {
    constructor() {
        this.role = null;
        this.platform = null;
        this.status = STATUS.UNINITIALIZED;
        this.localAgent = null;
        this.settings = null;
        this.agents = [];
        this.masterAgent = null;
        this.copierAgents = [];
        this.healthCheckTime = null;
        this.executor = null;
        this.counter = 0;
        this.lastPoints = null;
        // fixed random offset for stop loss buffer, random to avoid multiple accounts having same tp/sl
        // fixed value to avoid unreliable stop loss and take profit calculation
        this.stopLossBufferOffset = randomRange(0, 0.2); // max 15% * stopLossBuffer
        this.options = {
            enableStoploss: false,
        }
        this.statsLockUntil = 0;
        // Add processing lock
        this.processingServerRequest = false;
    }

    async init() {
        if (this.status == STATUS.INITIALIZED) {
            return;
        }
        try {
            await this.initializePlatform();
            this.status = STATUS.INITIALIZED;
            const platformName = await this.platform.GetName();
            log(`[app] copier app initialized successfully on ${platformName}`);
        } catch (error) {
            logError("[app] failed to initialize copier app:", error);
            throw error;
        }
    }

    almostEqual(a, b, epsilon = 0.001) {
        let result = Math.abs(a - b)/Math.max(Math.abs(a), Math.abs(b)) < epsilon;
        log("[app] almostEqual, a:", a, ", b:", b, ", result:", result);
        return result;
    }

    async run() {
        if (!this.platform) {
            logError("[app] platform is not initialized");
            return;
        }
        // Check platform status every second indefinitely
        while (true) {
            let loopStartTime = Date.now();
            try {
                if (Date.now() < this.statsLockUntil) {
                    log("[app] run loop, updating stats, skip the loop");
                    await new Promise((resolve) => setTimeout(resolve, 1000));
                    continue;
                }
                await this.platform.NavigateHome();
                this.counter++;
                const settings = await this.getSettings();
                log("[app] run loop, settings:", settings);

                // check backend health, no matter running or not
                let health =await this.checkBackendHealth();
                log("[app] run loop, check backend health", health, this.healthCheckTime, ', status:', this.status);
                if (!health) {
                    await new Promise((resolve) => setTimeout(resolve, 1000));
                    continue;
                }

                if (health.options) {
                    this.options = health.options;
                }

                // check if the extension version is updated
                // reload extension, reload the page
                if (health.extensionVersion && health.extensionVersion != version) {
                    log("[app] run loop, extension new version is available, reload extension, current version:", version, ", new version:", health.extensionVersion);
                    await this.reloadExtension();
                    continue;
                }

                // if server project status is changed, start /stop the app
                if (this.status != PROJECT_STATUS.RUNNING && health.projectStatus == PROJECT_STATUS.RUNNING) {
                    log("[app] run loop, project is running, start the app");
                    this.executor.setCooling();
                    this.resume();
                    await new Promise((resolve) => setTimeout(resolve, 1000));
                    continue;
                }

                // check if the page is need reload
                try {
                    let needReload = await this.platform.NeedReload();
                    if (needReload && this.counter % 10 == 0) {
                        log("[app] run loop, page is need reload, reload the page");
                        chrome.tabs.reload();
                        await new Promise((resolve) => setTimeout(resolve, 2000));
                        continue;
                    }
                } catch (error) {
                    logError("[app] run loop, failed to check if the page is need reload, error:", error);
                }

                // process server requests every 15 loops
                // this process is time-consuming, so we don't want to do it every loop
                // some type of the server request may need to stopping app, e.g. withdraw_all
                await this.processLatestServerRequest();

                let continueLoop = await this.checkServerStop(health);
                if (continueLoop) {
                    log("[app] run loop, server stop, skip the loop");
                    continue;
                }

                // check if there is any extension withdraw tx that is not done
                // currently not using server request because serverRequest isn't support callback to tx processing
                await this.processBalancerWithdraw();

                // hard refresh page based on refreshInterval setting
                let refreshMod = this.settings.refreshInterval * 60; // convert minutes to seconds
                let hardRefreshed = false;
                // checking risks to determine if the page is stucked
                if (this.settings.refreshInterval > 0 && this.counter % refreshMod == 0) {
                    log("[app] run loop, refresh page");
                    chrome.tabs.reload();
                    await new Promise((resolve) => setTimeout(resolve, 5*1000));
                    hardRefreshed = true;
                }
                
                // check if the page is service unavailable
                const isUnavailable = await this.checkPageServiceUnavailable();
                if (isUnavailable) {
                    log("[app] run loop, service is unavailable, skip the loop");
                    await new Promise((resolve) => setTimeout(resolve, 1 * 60 * 1000));
                    continue;
                }
                
                let riskNeedRefresh = false;
                // TODO: if the page is service unavailable, it will be early returned, don't worry about it here
                if (riskNeedRefresh && !hardRefreshed) {
                    log("[app] run loop, risk need refresh, skip the loop");
                    chrome.tabs.reload();
                    await new Promise((resolve) => setTimeout(resolve, 1000*10));
                    continue;
                }

                // check if there are open orders, close them
                let count = await this.platform.GetOpenOrdersCount();
                let stopLossEnabled = this.isStoplossEnabled();
                if (count > 0 && !stopLossEnabled) {
                    log(`[app] run loop, has open orders, ${this.role}: ${count} , stoploss isn't enabled, but there are open orders, cancel them`);
                    await this.platform.CloseAllOpenOrders();
                } else if (count > 2) {
                    log(`[app] run loop, has open orders, ${this.role}: ${count} , there are more than 2 open orders, cancel them`);
                    await this.platform.CloseAllOpenOrders();
                }

                // update agent, no matter running or not
                let agent = await this._updateAgent();
                log("[app] run loop, update agent:", agent);

                // if manual mode, set take profit and stop loss automatically
                if (stopLossEnabled) {
                    continueLoop = await this.setStoploss();
                    if (continueLoop) {
                        log("[app] run loop, stop reason:", continueLoop);
                        await new Promise((resolve) => setTimeout(resolve, 1000));
                        continue;
                    }
                }

                // update project status, prepare data for runloop
                // TODO: may move to runLoop, not here
                let project = await this._loadProject();
                log("[app] run loop, load project:", project);

                // if not running, only check unclosed positions, doing nothing else
                if (this.status == STATUS.RUNNING) {
                    this.lastTradeTime = await this.runLoop();
                } else {
                    log("[app] run loop, master is not running, skip the loop");
                    continueLoop = await this.checkUnclosedPositions();
                    if (continueLoop) {
                        await new Promise((resolve) => setTimeout(resolve, 1000));
                        continue;
                    }
                }
            } catch (error) {
                logError("[app] run loop, failed to do loop:", error);
            }
            // wait 1 second before next loop
            // because updateAgent() extract data from webpage, it's time-consuming, takes 1 second to get data
            // try to comment the sleep time here, to figure out why copy speed is too slow
            // await new Promise((resolve) => setTimeout(resolve, 1000));
            // sleep if loop time is less than 1 second
            let loopEndTime = Date.now();
            let loopTime = loopEndTime - loopStartTime;
            log("[app] run loop, loop time:", loopTime);
            let sleepTime = 1000 - loopTime;
            if (sleepTime > 0) {
                log("[app] run loop, sleep for:", sleepTime);
                await new Promise((resolve) => setTimeout(resolve, sleepTime));
            }
        }
    }

    async say(message) {
        await httpSay(this.settings.serverUrl, message);
    }

    isStoplossEnabled() {
        return this.options.enableStoploss;
    }

    // called from the run loop, return true if wanted to go into the next loop
    async checkServerStop(health) {
        let stopReason = "";
        if (this.status != PROJECT_STATUS.STOPPED && health.projectStatus == PROJECT_STATUS.STOPPED) {
            stopReason = "project stopped";
        }
        log("[app] run loop, status", this.status, ", project status:", health.projectStatus, ", project status comment:", health.projectStatusComment, ", stop reason:", stopReason);
        if (stopReason) {
            log("[app] run loop, project need to be stopped, stop the app, reason:", stopReason);
            this.executor.setCooling();

            // if the role is copier, stop the app after 2 seconds, give time to close positions
            if (this.role == COPIER) {
                await new Promise((resolve) => setTimeout(resolve, 2000));
                await this.stop();
            } else {
                await this.stop();
            }

            // in emergency, withdraw all the margin
            // in rebalance, withdraw the difference
            if (health.projectStatusComment == "emergency") {
                this.platform.FastWithdraw(this.settings.userId, 0);
                await this.updateStatus(PROJECT_STATUS.STOPPED, `reset_${health.projectStatusComment}`);
            }

            // wait 1 second before next loop
            await new Promise((resolve) => setTimeout(resolve, 1000));
            return true;
        }
        return false;
    }

    async checkUnclosedPositions() {
        if (this.status != STATUS.STOPPED) {
            return false;
        }
        if (!this.platform) {
            return false;
        }
        return await this._closePositions();
    }

    async processBalancerWithdraw() {
        let withdrawRequest = await this.httpGetExtensionWithdraw(this.settings.projectId, this.settings.userId);
        if (withdrawRequest && this.status != STATUS.RUNNING) {
            log("[app] run loop, project stopped, and extension withdraw tx is not done, withdraw the margin");
            await new Promise((resolve) => setTimeout(resolve, 500));
            let amount = 0;
            if (withdrawRequest.amount == 0) {
                amount = this.localAgent.totalMargin;
            } else {
                amount = withdrawRequest.amount;
            }
            if (amount >= 1.01) {
                let amountToWithdraw = Math.abs(amount);
                try {
                    await this.platform.FastWithdraw(this.settings.userId, amountToWithdraw);
                    await this.httpMarkExtensionWithdrawDone(this.settings.projectId, withdrawRequest.id);
                } catch (error) {
                    logError("[app] run loop, failed to withdraw, error:", error);
                }
            }
        }
    }

    // called from the run loop, return true if wanted to go into the next loop
    async setStoploss() {
        // for copier, check if the master has positions, if so, copy them
        log("[app] run loop, manual mode, master has positions, setting take profit and stop loss");
        if (this.masterAgent?.positions.length > 0) {
            for (const masterPosition of this.masterAgent.positions) {
                let currentPosition = this.localAgent.positions.find((p) => p.symbol === masterPosition.symbol);
                if (!currentPosition) {
                    log("[app] run loop, master in manual mode and has positions, copier setting take profit and stop loss, but current position not found");
                    return false;
                }

                // the output of calculateStopLoss() is null if the copier agent is not ready
                // the result is stable if the copier agent is ready
                // so we can compare the result with tp/sl price of current position
                let stopLossPrice = 0;
                let takeProfitPrice = 0;
                if (this.role == MASTER) {
                    let result = this.calculateStopLoss(masterPosition);
                    if (result == null) {
                        log("[app] run loop, master in manual mode and has positions, setting take profit and stop loss, but copier agent is not ready");
                        return true;
                    }
                    stopLossPrice = result.stopLossPrice;
                    takeProfitPrice = result.takeProfitPrice;
                    log("[app] run loop, master in manual mode, has positions, stop loss calculation result:", result);
                } else {
                    // for copier, if the tp/sl price is not set, set it to the master's tp/sl price
                    stopLossPrice = masterPosition.takeProfitPrice;
                    takeProfitPrice = masterPosition.stopLossPrice;
                    log("[app] run loop, copier in manual mode, has positions, set tp/sl price to master's tp/sl price:", stopLossPrice, takeProfitPrice);
                }
                if (!stopLossPrice || !takeProfitPrice) {
                    log("[app] run loop, copier in manual mode, has positions, tp/sl price is 0, skip the loop");
                    return true;
                }

                // tp/sl price not set, or tp/sl price is different from the master
                // set the the same take profit and stop loss as the master
                // but opposite side
                let change = !currentPosition.takeProfitPrice || !currentPosition.stopLossPrice;

                // log all the prices
                log("[app] master position, symbol:", masterPosition.symbol, ", takeProfitPrice:", takeProfitPrice, ", stopLossPrice:", stopLossPrice);
                log("[app] current position, symbol:", currentPosition.symbol, ", takeProfitPrice:", currentPosition.takeProfitPrice, ", stopLossPrice:", currentPosition.stopLossPrice);
                log("[app] has no tp/sl price, change:", change);
                if (!this.almostEqual(currentPosition.takeProfitPrice, takeProfitPrice, 0.01) || !this.almostEqual(currentPosition.stopLossPrice, stopLossPrice, 0.01)) {
                    change = true;
                }
                log("[app] tp/sl price mismatch, change:", change);
                if (change) {
                    // set the the same take profit and stop loss as the master
                    // but opposite side
                    await this.cancelOpenOrders();
                    await this.platform.SetTakeProfitStopLoss(currentPosition.symbol, takeProfitPrice, stopLossPrice);
                }
            }
        }
        return false;
    }

    async cancelOpenOrders() {
        await this.platform.CloseAllOpenOrders();
    }

    async checkPageServiceUnavailable() {
        const isUnavailable = await this.platform.GetServiceUnavailable();
        if (isUnavailable) {
            log("[app] service is unavailable, reload the page");
            // reload the page every 5 minutes
            chrome.tabs.reload();
        }
        return isUnavailable;
    }

    async extractStats() {
        let allTimePoints = 0;
        let allTimeVolume = 0;
        let allTimeProfit = 0;
        if (this.platform.GetName() == "aster") {
            return {
                allTimePoints: 0,
                allTimeVolume: 0,
                allTimeProfit: 0,
            };
        }
        let urls = this.platform.GetURLs(this.settings.defaultSymbol);
        // check if we are in loop of get stats
        this.navigateTo(urls.points);
        await new Promise((resolve) => setTimeout(resolve, 8000));
        try {
            let points = await this.platform.GetPoints();
            log("[app] get stats, points:", points);
            allTimePoints = points;
        } catch (error) {
            logError("[app] get stats, failed to get points:", error);
        }
        // TODO: get real volume and profit from portfolio
        // this.navigateTo(this.platform.GetURLs().portfolio);
        // await new Promise((resolve) => setTimeout(resolve, 4000));
        
        try {
            let  { volume, profit }  = await this.platform.GetVolumeAndProfit();
            log("[app] get stats, volume and profit:", volume, profit);
            allTimeVolume = volume;
            allTimeProfit = profit;
        } catch (error) {
            logError("[app] get stats, failed to get volume and profit:", error);
        }
        this.navigateTo(urls.trade);
        await new Promise((resolve) => setTimeout(resolve, 1000));
        let stats = {
            allTimePoints: allTimePoints,
            allTimeVolume: allTimeVolume,
            allTimeProfit: allTimeProfit,
            createTime: new Date().toISOString(),
        };
        if (allTimePoints > 0 ) {
            this.stats = stats;
        }
        log("[app] get stats, stats:", stats);
        return stats;
    }

    async getOneServerRequest() {
        if (this.currentAgent() == null || this.currentAgent().serverRequests == null) {
            return null;
        }
        let requests = [];
        for (const request of this.currentAgent().serverRequests) {
            // skip the request if it has been finished or the request is not for this agent
            if (request.finishTime || request.userId != this.settings.userId || request.projectId != this.settings.projectId) {
                continue;
            }
            requests.push(request);
        }
        if (requests.length == 0) {
            return null;
        }
        return requests[0];
    }

    // process server requests
    // some type of the server requests can't be processed if app is running, like update_stats
    async processLatestServerRequest() {
        if (this.processingServerRequest) {
            log("[app] process server request, already processing, skip");
            return;
        }

        this.processingServerRequest = true;
        try {
            let request = await this.getOneServerRequest();
            if (request == null) {
                log("[app] process server request, no request, skip");
                return;
            }
            if (request.requestType == "check_reload_page") {
                log("[app] process server request, check reload page");
                let reloadReason = "";
                try {
                    let margin = await this.platform.GetMargin();
                    if (margin && margin.totalMargin == 0) {
                        reloadReason = "margin is 0";
                    }
                } catch (error) {
                    reloadReason = "can't get margin";
                }
                if (reloadReason != "") {
                    log("[app] process server request, need reload page, reason:", reloadReason);
                    await this.reloadPage(request);
                } else {
                    log("[app] process server request, margin is not 0, no need reload page");
                    await httpFinishServerRequest(this.settings.serverUrl, request.requestId, "success", "success");
                }
            }

            log("[app] process server request, request:", request);
            if (request.requestType == "update_stats") {
                await this.updateStats(request);
            }

            if (request.requestType == "navigate_to_trade") {
                await this.navigateToTrade(request);
            }

            if (request.requestType == "withdraw_all") {
                await this.withdrawAll(request);
            }
        } catch (error) {
            logError("[app] process server request, error:", error);
        } finally {
            await this._loadProject(); // reload project to get the latest data, currentAgent() will be updated
            this.processingServerRequest = false;
        }
    }
    

    async runLoop() {
        if (!this.platform) {
            logError("[app] do run loop, platform is not initialized");
            return this.lastTradeTime;
        }
        const platformName = await this.platform.GetName();
        log("[app] do run loop, start executor:", this.executor.role, ", platform:", platformName, ", lastTradeTime:", this.lastTradeTime);

        this.executor.setSettings(this.settings);
        this.executor.setAgents(this.agents);
        
        // skip automatic trading if manual mode is enabled, for both master and copier
        // if master not in manual mode, the copier should not be in manual mode
        if (this.role == COPIER && !this.masterAgent.settings.manualMode) {
            this.settings.manualMode = false;
            this.saveSettings();
        }
        if (this.settings.manualMode) {
            log("[app] manual mode is enabled, skipping automatic trading");
            await new Promise((resolve) => setTimeout(resolve, 1000));
            return this.lastTradeTime;
        }
        
        this.lastTradeTime = await this.executor.do();
        log("[app] do run loop, end, lastTradeTime:", this.lastTradeTime);
        return this.lastTradeTime;
    }

    async _updateAgent(updateStats = false) {
        // get settings from storage
        const settings = await this.getSettings();
        const lastPrice = await this.platform.GetLastPrice(settings.defaultSymbol);
        const margin = await this.platform.GetMargin();
        const positions = await this.platform.GetPositions();
        const openOrders = await this.platform.GetOpenOrders();
        const createTime = new Date().toISOString();
        if (this.stats) {
            this.stats.portfolioValue = margin.totalMargin;
        }
        const agent = {
            role: this.role,
            platform: settings.platform || "",
            userId: settings.userId || "",
            alias: settings.alias || "",
            projectId: settings.projectId || "",
            symbol: settings.defaultSymbol || "",
            copyFromId: settings.copyFromId || "",
            copyPercentage: settings.copyPercentage || 0,
            lastPrice: lastPrice.lastPrice || 0,
            spread: lastPrice.spread || 0,
            totalMargin: margin.totalMargin || 0,
            availableMargin: margin.availableMargin || 0,
            positions: positions || [],
            openOrders: openOrders || [],
            createTime: createTime,
            settings: settings,
            version: version,
            stats: this.stats,
        };
        this.localAgent = agent;
        httpUpdateAgent(this.settings.serverUrl, agent);
        return agent;
    }

    async checkRisk() {
        risks = [];
        return risks;
    }


    // get stop loss buffer from settings, validate the value, and use the value from master agent if available
    // add a small randomness to prevent multiple accounts having same tp/sl
    getStopLossBuffer() {
        let stopLossBuffer = 0.01;
        if (this.role == MASTER) {
            stopLossBuffer = this.settings.stopLossBuffer || 0.01;
        } else if (this.masterAgent?.settings.stopLossBuffer) {
            stopLossBuffer = this.masterAgent.settings.stopLossBuffer;
        }
        // validate the stop loss buffer value, if in valid range, use the value from master agent
        // otherwise, use the default value 0.01
        if (stopLossBuffer < 0 || stopLossBuffer > 0.05) {
            stopLossBuffer = 0.01;
        }
        
        let randomShift = stopLossBuffer*this.stopLossBufferOffset; // Add small randomness to prevent multiple accounts having same tp/sl
        let buffer = stopLossBuffer + randomShift;
        return buffer;
    }


    // use existing master position and simulated copier position to calculate the take profit and stop loss price
    // find the tightest take profit and stop loss price from master and copier
    // return null if copier agent is not ready
    calculateStopLoss(masterPosition) {
        if (this.copierAgent() == null) {
            logError("[app] simulateStopLoss, copier agent is not ready");
            return null;
        }
        let masterResult = this._calculateStopLoss(masterPosition);
        let copierPosition = null;
        for (const position of this.copierAgent().positions) {
            if (position.symbol == masterPosition.symbol) {
                copierPosition = position;
                break;
            }
        }
        if (copierPosition == null) {
            logError("[app] simulateStopLoss, copier position is not found");
            return null;
        }
        let copierResult = this._calculateStopLoss(copierPosition);

        // For hedged positions, we need to find a price level where:
        // 1. Both positions can close safely (before liquidation)
        // 2. The levels should be symmetric (if master closes at X, copier closes at X)
        // Consider an example:
        // we long 1 btc at 10000 on master, and short 1 btc at 10000 on copier
        // master has 5000 in margin, and copier has 4000 in margin
        //
        // Example:
        // Master (Long):
        // - Entry: $10,000
        // - mLIQ: $5,000
        // - mSL: $5,500 (safe above liquidation)
        // - mTP: $14,500
        //
        // Copier (Short):
        // - Entry: $10,000
        // - cLIQ: $14,000
        // - cSL: $13,500 (safe below liquidation)
        // - cTP: $6,500
        //
        // The safe combination would be:
        // Master: SL at $5,500, TP at $13,500
        // Copier: SL at $13,500, TP at $5,500
        //
        // Because:
        // At $5,500:
        // - Master closes at SL (safe above mLIQ $5,000)
        // - Copier closes at TP (safe below cLIQ $14,000)
        // At $13,500:
        // - Master closes at TP (safe above mLIQ $5,000)
        // - Copier closes at SL (safe below cLIQ $14,000)
        let markPrice = masterPosition.markPrice;
        let prices = [
            { price: masterResult.stopLossPrice, source: 'mSL' },
            { price: masterResult.takeProfitPrice, source: 'mTP' },
            { price: copierResult.stopLossPrice, source: 'cSL' },
            { price: copierResult.takeProfitPrice, source: 'cTP' }
        ];
        let stopLossBuffer = this.getStopLossBuffer();
        log("[app] simulateStopLoss, prices:", prices);
        log("[app] simulateStopLoss, master liquidation price:", masterPosition.liquidationPrice);
        log("[app] simulateStopLoss, copier liquidation price:", copierPosition.liquidationPrice);
        log("[app] simulateStopLoss, stopLossBuffer:", stopLossBuffer);
        
        // For both long and short positions:
        // - Find closest price to master liquidation price that maintains buffer
        // - Find closest price to copier liquidation price that maintains buffer
        let stopLossPrice;
        let takeProfitPrice;
        if (masterPosition.side === "long") {
            // For long positions, find closest price to master liquidation price for stop loss
            let safeStopLossPrices = prices
                .filter(p => p.price >= masterPosition.liquidationPrice * (1 + stopLossBuffer))
                .sort((a, b) => Math.abs(a.price - masterPosition.liquidationPrice) - Math.abs(b.price - masterPosition.liquidationPrice));
            log("[app] simulateStopLoss, safe stop loss prices for long:", safeStopLossPrices);
            stopLossPrice = safeStopLossPrices[0]?.price || masterResult.stopLossPrice;

            // For long positions, find closest price to copier liquidation price for take profit
            let safeTakeProfitPrices = prices
                .filter(p => p.price <= copierPosition.liquidationPrice * (1 - stopLossBuffer))
                .sort((a, b) => Math.abs(a.price - copierPosition.liquidationPrice) - Math.abs(b.price - copierPosition.liquidationPrice));
            log("[app] simulateStopLoss, safe take profit prices for long:", safeTakeProfitPrices);
            takeProfitPrice = safeTakeProfitPrices[0]?.price || copierResult.stopLossPrice;

            if (stopLossPrice > markPrice || takeProfitPrice < markPrice) {
                log("[app] simulateStopLoss, stopLossPrice or takeProfitPrice is out of range, return null");
                return null;
            }
        } else {
            // For short positions, find closest price to master liquidation price for stop loss
            let safeStopLossPrices = prices
                .filter(p => p.price <= masterPosition.liquidationPrice * (1 - stopLossBuffer))
                .sort((a, b) => Math.abs(a.price - masterPosition.liquidationPrice) - Math.abs(b.price - masterPosition.liquidationPrice));
            log("[app] simulateStopLoss, safe stop loss prices for short:", safeStopLossPrices);
            stopLossPrice = safeStopLossPrices[0]?.price || masterResult.stopLossPrice;
            // For short positions, find closest price to copier liquidation price for take profit
            let safeTakeProfitPrices = prices
                .filter(p => p.price >= copierPosition.liquidationPrice * (1 + stopLossBuffer))
                .sort((a, b) => Math.abs(a.price - copierPosition.liquidationPrice) - Math.abs(b.price - copierPosition.liquidationPrice));
            log("[app] simulateStopLoss, safe take profit prices for short:", safeTakeProfitPrices);
            takeProfitPrice = safeTakeProfitPrices[0]?.price || copierResult.stopLossPrice;

            if (stopLossPrice < markPrice || takeProfitPrice > markPrice) {
                log("[app] simulateStopLoss, stopLossPrice or takeProfitPrice is out of range, return null");
                return null;
            }
        }

        let result = {
            stopLossPrice: stopLossPrice,
            takeProfitPrice: takeProfitPrice
        };
        log("[app] simulateStopLoss, final result:", result);

        log("[app] hedged stop loss levels:", {
            symbol: masterPosition.symbol,
            side: masterPosition.side,
            result
        });

        return result;
    }

    // calculate take profit and stop loss price
    // prevent multiple accounts set the same tp/sl price, give a little randomness and buffer
    // the ratio is 0.85-0.9, which is 15%-10% of the difference between entry price and liquidation price
    // the tp/sl price are capped at 2x and 0.5x of the entry price, to prevent too far away from entry price
    _calculateStopLoss(position) {
        let entryPrice = position.entryPrice;
        let liquidationPrice = position.liquidationPrice;
        let buffer = this.getStopLossBuffer();
        
        log("[app] calculate stop loss, position:", position, ", entryPrice:", entryPrice, ", liquidationPrice:", liquidationPrice, ", buffer:", buffer);
        
        let result = {
            takeProfitPrice: 0,
            stopLossPrice: 0,
        };

        if (position.side == "long") {
            // For long positions:
            // - Stop loss is below entry price
            // - Take profit is above entry price
            result.stopLossPrice = Math.max(entryPrice * 0.5, liquidationPrice * (1 + buffer));
            result.takeProfitPrice = Math.min(entryPrice * 2, entryPrice + (entryPrice - result.stopLossPrice));
        } else {
            // For short positions:
            // - Stop loss is above entry price
            // - Take profit is below entry price
            result.stopLossPrice = Math.min(entryPrice * 2, liquidationPrice * (1 - buffer));
            result.takeProfitPrice = Math.max(entryPrice * 0.5, entryPrice - (result.stopLossPrice - entryPrice));
        }
        
        log("[app] calculate stop loss, result:", result);
        return result;
    }

    async _loadProject() {
        let response = await httpGetProject(this.settings.serverUrl, this.settings.projectId);
        log("[app] get project response:", response);

        if (!response) {
            throw new Error("[app] get project response is null");
        }

        this.agents = response.project.agents;
        
        // Make sure to set agents in executor
        if (this.executor) {
            this.executor.setAgents(this.agents);
        }

        let masterAgents = [];
        let copierAgents = [];
        for (const agent of this.agents) {
            if (agent.role === MASTER) {
                masterAgents.push(agent);
            } else {
                copierAgents.push(agent);
            }
        }
        if (masterAgents.length === 0 || masterAgents.length > 1) {
            logError("[app] get agents error, master agents:", masterAgents);
            return null;
        }
        this.masterAgent = masterAgents[0];
        this.copierAgents = copierAgents;

        // pull server settings
        for (const agent of this.agents) {
            if (agent.userId == this.settings.userId) {
                if (this.settings.version == null ||agent.serverSettings.version > this.settings.version) {
                    // Always copy version from serverSettings
                    this.settings.version = agent.serverSettings.version;
                    
                    // Copy other fields that exist in serverSettings
                    Object.keys(agent.serverSettings).forEach(key => {
                        if (this.settings.hasOwnProperty(key)) {
                            this.settings[key] = agent.serverSettings[key];
                        }
                    });
                    log("[app] pull server settings:", agent.serverSettings, ", local settings:", this.settings);
                    await this.saveSettings();
                } else {
                    log("[app] server version is not newer, skipping update");
                }
            }
        }

        return response.project;
    }

    async getAgents() {
        let masterAgent = this._getMasterAgent();
        let copierAgents = this._getCopierAgents();
        if (this.role === MASTER) {
            return {
                masterAgent: masterAgent,
                copierAgents: copierAgents,
            };
        } else {
            copierAgents = [];
            for (const agent of this.copierAgents) {
                if (agent.userId == this.settings.userId) {
                    copierAgents.push(agent);
                }
            }
            return {
                masterAgent: masterAgent,
                copierAgents: copierAgents,
            };
        }
    }

    navigateTo(url) {
        // Use Chrome's tab API to update the URL only if different
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            if (tabs[0] && tabs[0].url !== url) {
                chrome.tabs.update(tabs[0].id, { url: url });
            }
        });
    }

    _getMasterAgent() {
        if (this.role === MASTER) {
            return this.localAgent;
        }
        if (!this.masterAgent) {
            return null;
        }
        return this.masterAgent;
    }

    _getCopierAgents() {
        if (this.role === COPIER) {
            return [this.localAgent];
        }
        if (!this.copierAgents) {
            return [];
        }
        return this.copierAgents;
    }

    currentAgent() {
        for (const agent of this.agents) {
            if (agent.userId == this.settings.userId) {
                return agent;
            }
        }
        return null;
    }

    copierAgent() {
        if (this.copierAgents.length === 0) {
            return null;
        }
        return this.copierAgents[0];
    }

    async httpGetExtensionWithdraw(projectId, userId) {
        try {
            let response = await httpGetExtensionWithdraw(this.settings.serverUrl, projectId, userId);
            log("[app] get extension withdraw:", response);
            return response.tx;
        } catch (error) {
            logError("[app] failed to get extension withdraw:", error);
            return null;
        }
    }

    async httpMarkExtensionWithdrawDone(projectId, txRefId) {
        try {
            let response = await httpMarkExtensionWithdrawDone(this.settings.serverUrl, projectId, txRefId);
            log("[app] mark extension withdraw done:", response);
            return response.success;
        } catch (error) {
            logError("[app] failed to mark extension withdraw done:", error);
            return false;
        }
    }

    async httpGetProjectRisks(projectId) {
        try {
            let response = await httpGetProjectRisks(this.settings.serverUrl, projectId);
            log("[app] get project risks:", response);
            return response.risks;
        } catch (error) {
            logError("[app] failed to get project risks:", error);
            return [];
        }
    }

    async _closePosition(position) {
        log(`[app] closing position:`, position);
        let side = reverseSide(position.side);
        let size = Math.abs(position.size);
        await this.executor._createMarketOrder(position.symbol, side, size, true);
    }

    // closed position, return true
    // no position to close, return false
    async _closePositions() {
        let positions = await this.platform.GetPositions();
        if (positions.length > 0) {
            log("[app] run loop, has unclosed positions, close them");
            for (const position of positions) {
                await this._closePosition(position);
            }
            return true;
        }
        return false;
    }


    resume(isManual = false) {
        this.status = STATUS.RUNNING;
        log("[app] copier app resumed");
        // master / copier both can update the server project status
        if (isManual) {
            this.updateStatus(PROJECT_STATUS.RUNNING, "extension");
        }
    }

    async stop(isManual = false) {
        this.status = STATUS.STOPPED;
        // also close positions under manual mode
        await this.executor.closePositions();
        await new Promise((resolve) => setTimeout(resolve, 2000));
        await this.checkUnclosedPositions();
        // master / copier both can update the server project status
        if (isManual) {
            await this.updateStatus(PROJECT_STATUS.STOPPED, "extension");
        }
        log("[app] copier app stopped");
    }

    async getStatus() {
        return new Promise((resolve) => {
            let backendOnline = false;
            if (this.healthCheckTime && this.healthCheckTime > Date.now() - 10000) {
                backendOnline = true;
            }
            const status = {
                status: this.status,
                platform: this.platform ? this.platform.GetName() : "",
                backendStatus: backendOnline ? "online" : "offline",
            };
            resolve(status);
        });
    }

    async checkBackendHealth() {
        try {
            const response = await httpCheckHealth(this.settings.serverUrl, this.settings.projectId);
            this.healthCheckTime = Date.now();
            return response;
        } catch (error) {
            logError("[app] failed to check backend health:", error);
            return false;
        }
    }

    async getSettings() {
        const defaultSettings = {
            coolingHour: 1.0,
            refreshInterval: 30,
            manualMode: false,
            stopLossBuffer: 0.01,
            longRatio: 0.5,
        };

        const settings = await new Promise((resolve) => {
            chrome.storage.sync.get([
                "platform", "userId", "projectId", "defaultSymbol", 
                "copyFromId", "copyPercentage", "serverUrl", "minMargin", 
                "defaultSize", "coolingHour", "refreshInterval", 
                "debug", "manualMode", "alias", "version", "stopLossBuffer", "longRatio",
            ], resolve);
        });

        this.settings = {
            ...defaultSettings,
            ...settings
        };

        return this.settings;
    }

    async saveSettings() {
        if (!this.settings.version) {
            log("[app] save settings, version is not set, skip");
            return;
        }
        chrome.storage.sync.set(this.settings, () => {
            log("[app] save settings:", this.settings);
        });
    }


    async initializePlatform() {
        if (this.platform) {
            return;
        }
        const settings = await this.getSettings();
        this.settings = settings;
        log("[app] init, settings:", this.settings);
        switch (settings.platform) {
            case "lighter":
                this.platform = LighterPlatform;
                break;
            case "aster":
                this.platform = AsterPlatform;
                break;
            default:
                throw new Error(`unsupported platform: ${settings.platform}`);
        }
        if (this.settings.copyPercentage == 0) {
            this.role = MASTER;
            this.executor = new Master({ settings: this.settings, app: this, platform: this.platform });
        } else {
            this.role = COPIER;
            this.executor = new Copier({ settings: this.settings, app: this, platform: this.platform });
        }
        log("[app] executor:", this.executor);

    }

    async _testWithdraw() {
        await this.platform.FastWithdraw(this.settings.userId, 10.0);
    }

    async reloadPage(request) {
        log("[app] reloading page, request id:", request.requestId);
        try {
            // Reload the page
            chrome.tabs.reload();
            await httpFinishServerRequest(this.settings.serverUrl, request.requestId, "success", "reloaded page");
        } catch (error) {
            logError("[app] failed to reload extension:", error);
            throw error;
        }
    }

    async reloadExtension() {
        log("[app] reloading extension");
        try {
            // Reload the extension
            chrome.runtime.reload();
            chrome.tabs.reload();
        } catch (error) {
            logError("[app] failed to reload extension:", error);
            throw error;
        }
    }

    // click update stats button, request is null, update stats as well
    async updateStats(request) {
        this.statsLockUntil = Date.now() + 15000; // lock for 15 seconds
        let status = "success";
        let comment = "";
        // only get stats if app isn't running
        if (this.status != STATUS.RUNNING) {
            let stats = await this.extractStats();
            log("[app] update stats:", stats);
            
            if (stats.allTimePoints > 0 || stats.allTimeVolume > 0) {
                status = "success";
                comment = "";
            } else {
                status = "failed";
                comment = "stats is 0";
            }
        } else {
            status = "failed";
            comment = "app is running";
        }
        if (request) {
            await httpFinishServerRequest(this.settings.serverUrl, request.requestId, status, comment);
        } else {
            await httpFinishServerRequest(this.settings.serverUrl, "", status, comment);
        }
    }

    async navigateToTrade(request) {
        log("[app] navigate to trade, request id:", request.requestId);
        let urls = this.platform.GetURLs(this.settings.defaultSymbol);
        await this.navigateTo(urls.trade);
        await httpFinishServerRequest(this.settings.serverUrl, request.requestId, "success", "navigated to trade");
    }

    async withdrawAll(request) {
        let status = "success";
        let comment = "";
        // only withdraw if app isn't running
        if (this.status == STATUS.RUNNING) {
            await this.stop(true);
            await new Promise((resolve) => setTimeout(resolve, 3000));
        }
        try {
            await this.platform.FastWithdraw(this.settings.userId, 0);
        } catch (error) {
            logError("[app] process server request, failed to withdraw all:", error);
            status = "failed";
            comment = "withdraw all failed";
        }
        if (request) {
            await httpFinishServerRequest(this.settings.serverUrl, request.requestId, status, comment);
        } else {
            await httpFinishServerRequest(this.settings.serverUrl, "", status, comment);
        }
    }

    async updateStatus(status, comment = '') {
        try {
            const response = await httpUpdateStatus(this.settings.serverUrl, this.settings.projectId, status, comment);
            if (response.success) {
                // After status change, reload the project to get fresh data
                // await this._loadProject();
                return true;
            }
            return false;
        } catch (error) {
            logError('Failed to toggle status:', error);
            return false;
        }
    }
}

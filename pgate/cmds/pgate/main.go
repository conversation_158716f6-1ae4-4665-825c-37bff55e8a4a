package main

import (
	"flag"
	"fmt"
	"os"
	"os/signal"
	"path"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"

	"github.com/mitchellh/go-homedir"
	"github.com/wizhodl/pgate"
	"github.com/wizhodl/pgate/license"
	"github.com/wizhodl/quanter/common/zlog"

	"github.com/wizhodl/quanter/secrets"
	"github.com/wizhodl/quanter/utils"
)

var (
	buildTime  string // 编译时间
	commitHash string // git commit hash
)

var _proxies = []*pgate.Proxy{}
var _templates = []string{}

var pgateClient *pgate.PgateClient

func checkLicense(configPath string) error {
	// parse build time from ldflags, format: $(date +%Y-%m-%d.%H:%M:%S)
	var maxExpireTime *time.Time

	// parse configDir from configPath
	configDir := path.Dir(configPath)
	licenseManager, err := license.NewTimeLockLicenseManager(license.LicenseProjectPgate, buildTime, maxExpireTime, 365*24*time.Hour, path.Join(configDir, "licenses.json"))
	if err != nil {
		zlog.Errorf("new license manager failed: %v", err)
		return fmt.Errorf("new license manager failed: %v", err)
	}
	if err := licenseManager.Check(); err != nil {
		zlog.Errorf("license check failed: %v", err)
		return fmt.Errorf("license check failed: %v", err)
	}
	return nil
}

func deletedProxies() (proxies []*pgate.Proxy) {
	for _, p := range _proxies {
		if p.DeletedAt != nil {
			proxies = append(proxies, p)
		}
	}
	return
}

func activeProxies() (proxies []*pgate.Proxy) {
	for _, p := range _proxies {
		if p.DeletedAt == nil {
			proxies = append(proxies, p)
		}
	}
	return
}

func getProxyByName(name string) (proxy *pgate.Proxy) {
	for _, p := range _proxies {
		if p.Name == name {
			proxy = p
			break
		}
	}
	return
}

func main() {
	var isServer bool
	var configPath string
	var port int
	myFlags := flag.NewFlagSet("pgate/main", flag.ExitOnError)

	myFlags.BoolVar(&isServer, "server", false, "run as server")
	myFlags.StringVar(&configPath, "config", "", "config file path")
	myFlags.IntVar(&port, "port", 6979, "server port, use for client")
	myFlags.Parse(os.Args[1:])

	if isServer {
		server(configPath)
	} else {
		client(port)
	}
}

// go run -ldflags "-X main.buildTime=2024-04-01.12:00:00" main.go -server -proxy_config ~/.proxy/config.yaml
func server(config string) {
	fmt.Printf("Build: %s/(%s)\n", commitHash, buildTime)

	defaultConfig, err := homedir.Expand("~/.proxy/config.yaml")
	if err != nil {
		zlog.Errorf("expand config path failed: %v", err)
		panic(err)
	}

	if strings.HasPrefix(config, "~") {
		absPath, err := homedir.Expand(config)
		if err != nil {
			zlog.Errorf("expand -config path failed: %v", err)
			panic(err)
		}
		config = absPath
	}

	if config == "" {
		config = defaultConfig
	}

	// 检查许可证
	if err := checkLicense(config); err != nil {
		fmt.Printf("许可证检查失败: %v\n", err)
		os.Exit(1)
	}

	debug, _ := strconv.ParseBool(os.Getenv("PGATE_DEBUG"))
	if debug {
		passwd := os.Getenv("PGATE_PASSWORD")
		if passwd != "" {
			secrets.SkipAuthCode(true) // 调试模式下，跳过 2FA 验证
			if err := secrets.CheckPassword(passwd, ""); err != nil {
				zlog.Panicf("check password failed: %v", err)
				return
			} else {
				zlog.Infof("check password ok")
			}
		}
	} else {
		passwd := utils.SurveyPassword("Password")
		if passwd != "" {
			authCode := utils.SurveyInput("2FA")

			if err := secrets.CheckPassword(passwd, authCode); err != nil {
				zlog.Panicf("check password failed: %v", err)
				return
			} else {
				zlog.Infof("check password ok")
			}
		}
	}

	opts := pgate.ProxyOptions{}
	err = opts.Load(config)
	if err != nil {
		zlog.Errorf("load config failed: %v", err)
		panic(err)
	}

	zlogger := zlog.NewRotateLogger("DEBUG", fmt.Sprintf("%s/%s", opts.DataDir, "proxy.log"), nil)
	zlog.SetLogger(zlogger)

	fmt.Println("配置加载成功")

	manager, err := pgate.NewProxyManager(&opts, nil)
	if err != nil {
		panic(err)
	}
	if manager.Options.GatewayServer.Enabled {
		zlog.Debugf("starting proxy gateway...")
		gateway := pgate.NewProxyGateway(manager)
		manager.Gateway = gateway
		var wg sync.WaitGroup

		// List of addresses to bind
		addresses := []string{fmt.Sprintf(":%d", manager.Options.GatewayServer.Port)}
		// Start a server for each address
		for _, addr := range addresses {
			wg.Add(1)
			go gateway.StartServer(addr, &wg)
		}

		c := make(chan os.Signal, 1)
		signal.Notify(c, os.Interrupt, syscall.SIGTERM)
		go func() {
			<-c
			zlog.Infof("got interrupt signal")
			gateway.ClearSurgeConfig()
			os.Exit(0)
		}()

		// Wait for all servers to finish
		wg.Wait()
	}
	zlog.Errorf("gateway not enabled, exit...")
}

func client(port int) {
	// get PGATE_HOST from env
	if os.Getenv("PGATE_HOST") != "" {
		host := os.Getenv("PGATE_HOST")
		pgateClient = pgate.NewPgateClient(host)
	} else {
		pgateClient = pgate.NewPgateClient(fmt.Sprintf("127.0.0.1:%d", port))
	}
	for {
		err := refreshProxiesAndTemplates()
		if err != nil {
			zlog.Errorf("refresh proxies and templates failed: %v", err)
			time.Sleep(3 * time.Second)
			continue
		}

		choices := []string{
			"1. 查看所有代理",
			"2. 测试全部代理",
			"3. 创建代理",
			"4. 创建 SOCKS5 代理",
			"5. 删除代理",
			"6. 修复代理",
			"7. 停止/启动/维护代理",
			"8. 增加代理有效期",
			"9. 查看已删除代理",
			"10. 打印转发器",
			"11. 打印预留实例/承诺使用折扣",
			"12. 同步云存储代理",
			"13. 项目设置",
			"Q. 退出",
		}
		choice, _ := utils.SurveySelect("请选择操作:", choices)

		switch choice {
		case 0:
			err := refreshProxiesAndTemplates()
			if err != nil {
				zlog.Errorf("refresh proxies and templates failed: %v", err)
				time.Sleep(3 * time.Second)
				continue
			}
			fmt.Println(pgate.FormatProxies(activeProxies(), true))
		case 1:
			testAllProxies()
		case 2:
			promptCreateProxies()
		case 3:
			promptCreateSocks5Proxy()
		case 4:
			promptDeleteProxies()
		case 5:
			promptRepairProxy()
		case 6:
			stopOrStart, _ := utils.SurveySelect("选择操作", []string{"停止代理", "启动代理", "删除代理日志"})
			if stopOrStart == 0 {
				promptStopProxy()
			} else if stopOrStart == 1 {
				promptStartProxy()
			} else if stopOrStart == 2 {
				promptRemoveLogForProgram()
			}
		case 7:
			promptIncreaseLifeHour()
		case 8:
			fmt.Println(formatDeletedProxies())
		case 9:
			promptPrintForwarders()
		case 10:
			promptPrintReservedInstances()
		case 11:
			promptSyncCloudStorage()
		case 12:
			promptProjectSettings()

		}

		if choice == len(choices)-1 {
			break
		}
	}
}

func promptProjectSettings() {
	choices := []string{
		"1. 设置账户数量限制",
		"2. 查看账户数量限制",
		"3. 删除账户数量限制",
		"Q. 退出",
	}
	choice, _ := utils.SurveySelect("请选择操作:", choices)

	switch choice {
	case 0:
		promptSetAccountLimit()
	case 1:
		printAccountLimit()
	case 2:
		promptDeleteAccountLimit()
	}
}

func promptSetAccountLimit() {
	project := utils.SurveyInput("请输入项目名称:")
	proxyGroup := utils.SurveyInput("请输入代理组名称（可以为空）:")
	limit := utils.SurveyInt("请输入账户数量限制:")

	err := pgateClient.SetAccountLimit(project, proxyGroup, limit)
	if err != nil {
		zlog.Errorf("set account limit failed: %v", err)
	} else {
		fmt.Println("设置账户数量限制成功")
	}
}

func promptDeleteAccountLimit() {
	project := utils.SurveyInput("请输入项目名称:")
	proxyGroup := utils.SurveyInput("请输入代理组名称（可以为空）:")

	err := pgateClient.SetAccountLimit(project, proxyGroup, 0)
	if err != nil {
		zlog.Errorf("delete account limit failed: %v", err)
	} else {
		fmt.Println("删除账户数量限制成功")
	}
}

func printAccountLimit() {
	limits, err := pgateClient.GetProjectAccountLimits()
	if err != nil {
		zlog.Errorf("get project account limits failed: %v", err)
	} else {
		fmt.Println(pgate.FormatProjectAccountLimits(limits))
	}
}

func promptPrintForwarders() {
	choices := []string{
		"1. 打印登录状态",
		"2. 打印转发器",
		"3. 打印组转发器",
		"4. 清除登录账户",
		"Q. 退出",
	}
	choice, _ := utils.SurveySelect("请选择操作:", choices)

	switch choice {
	case 0:
		fmt.Println(formatLoginForwarders())
	case 1:
		fmt.Println(formatForwarders())
	case 2:
		fmt.Println(formatGroupForwarders())
	case 3:
		promptClearAccountsForProxy()
	}
}

func refreshProxiesAndTemplates() (er error) {
	_proxies, er = pgateClient.ListProxies()
	if er != nil {
		return
	}
	_templates, er = pgateClient.ListProxyTemplates()
	if er != nil {
		return
	}
	return
}

func promptCreateProxies() {
	var template string
	if len(_templates) == 0 {
		fmt.Println("没有可用模板")
		return
	} else if len(_templates) == 1 {
		fmt.Println("只有一个模板，已选择:", _templates[0])
		template = _templates[0]
	} else {
		_, template = utils.SurveySelect("请选择模板:", _templates)
	}

	index, _ := utils.SurveySelect("创建方式", []string{"批量创建", "一个代理"})
	if index == 0 {
		group := ""
		count := 0
		lifeHour := 0
		for {
			group = utils.SurveyInput("组名称:")
			group = strings.ToLower(strings.TrimSpace(group))
			if group == "" {
				fmt.Println("组名称不能为空")
				continue
			}
			err := pgate.CheckGroupName(group)
			if err != nil {
				fmt.Println(err)
				continue
			} else {
				break
			}
		}

		for {
			count = utils.SurveyInt("代理数量:")
			if count <= 0 {
				fmt.Println("代理数量必须大于 0")
			} else {
				break
			}
		}

		for {
			lifeHour = utils.SurveyInt("代理运行时长(小时):")
			if lifeHour < 0 {
				fmt.Println("代理运行时长必须大于等于 0，小于 2160 小时")
			} else {
				break
			}
		}

		confirmMsg := "将使用组名称 " + group + " 创建 " + strconv.Itoa(count) + " 个代理，确认吗?"
		if !utils.SurveyYes(confirmMsg) {
			return
		}

		for i := 0; i < count; i++ {
			name := pgate.GenerateRandomName(4, true)
			proxy, err := pgateClient.CreateProxy(fmt.Sprintf("%s-%s", group, name), template, lifeHour, group)
			if err != nil {
				zlog.Errorf("create proxy failed: %v", err)
				return
			}

			fmt.Println("代理创建中，配置信息：")
			fmt.Println("域名:", proxy.GetDomain())
			fmt.Println("端口:", 443)
			fmt.Println("密码:", proxy.Password)
		}
	} else {
		name := os.Getenv("DEBUG_PROXY_NAME")
		if name == "" {
			for {
				name = utils.SurveyInput("请输入代理名称，留空则自动生成:")
				if name == "" {
					name = pgateClient.SuggestName(template)
				}

				err := pgateClient.CheckName(name)
				if err != nil {
					if strings.Contains(err.Error(), "name exist") {
						fmt.Println("代理名称已存在，请重新输入")
						continue
					}
					fmt.Printf("检查代理是否存在失败: %v\n", err)
					return
				} else {
					break
				}
			}
		}

		lifeHourEnv := os.Getenv("DEBUG_PROXY_LIFE_HOUR")
		lifeHour := 0
		if lifeHourEnv != "" {
			lifeHour, _ = strconv.Atoi(lifeHourEnv)
		} else {
			lifeHour = utils.SurveyInt("请输入代理生命周期(小时), 留空则为不限制:")
		}

		confirmMsg := "将使用名称 " + name + " 创建代理，确认吗?"
		if lifeHour > 0 {
			confirmMsg = "将使用名称 " + name + " 创建代理，生命周期 " + strconv.Itoa(lifeHour) + " 小时，确认吗?"
		}

		if os.Getenv("DEBUG_PROXY_NAME") == "" {
			if !utils.SurveyYes(confirmMsg) {
				return
			}
		}

		proxy, err := pgateClient.CreateProxy(name, template, lifeHour, "")
		if err != nil {
			zlog.Errorf("create proxy failed: %v", err)
			return
		}

		fmt.Println("代理创建中，配置信息：")
		fmt.Println("域名:", proxy.GetDomain())
		fmt.Println("端口:", 443)
		fmt.Println("密码:", proxy.Password)
	}
}

func promptCreateSocks5Proxy() {
	index, _ := utils.SurveySelect("创建方式", []string{"批量创建", "单个代理"})

	if index == 0 {
		promptCreateSocks5ProxyGroup()
	} else {
		promptCreateSingleSocks5Proxy()
	}
}

func promptCreateSocks5ProxyGroup() {
	// Get group name
	group := ""
	for {
		group = utils.SurveyInput("组名称:")
		group = strings.ToLower(strings.TrimSpace(group))
		if group == "" {
			fmt.Println("组名称不能为空")
			continue
		}
		err := pgate.CheckGroupName(group)
		if err != nil {
			fmt.Println(err)
			continue
		} else {
			break
		}
	}

	// Get count
	count := 0
	for {
		count = utils.SurveyInt("代理数量:")
		if count <= 0 {
			fmt.Println("代理数量必须大于 0")
		} else {
			break
		}
	}

	// Get life hour
	lifeHourEnv := os.Getenv("DEBUG_PROXY_LIFE_HOUR")
	lifeHour := 0
	if lifeHourEnv != "" {
		lifeHour, _ = strconv.Atoi(lifeHourEnv)
	} else {
		lifeHour = utils.SurveyInt("代理运行时长(小时), 留空则为不限制:")
	}

	// Collect server information
	servers := []map[string]string{}
	fmt.Printf("请输入 %d 个 SOCKS5 服务器信息:\n", count)

	for i := 0; i < count; i++ {
		fmt.Printf("\n=== 服务器 %d/%d ===\n", i+1, count)

		serverInfos := parseServerInfo()
		if serverInfos == nil {
			fmt.Println("跳过此服务器")
			continue
		}

		servers = append(servers, serverInfos...)

		// If we've collected enough servers, break out of the loop
		if len(servers) >= count {
			break
		}
	}

	if len(servers) == 0 {
		fmt.Println("没有有效的服务器信息，取消创建")
		return
	}

	// Confirm creation
	confirmMsg := fmt.Sprintf("将使用组名称 %s 创建 %d 个 SOCKS5 代理，确认吗?", group, len(servers))
	if lifeHour > 0 {
		confirmMsg = fmt.Sprintf("将使用组名称 %s 创建 %d 个 SOCKS5 代理，生命周期 %d 小时，确认吗?", group, len(servers), lifeHour)
	}

	if !utils.SurveyYes(confirmMsg) {
		return
	}

	// Create proxies
	for i, server := range servers {
		name := pgate.GenerateRandomName(4, true)
		fullName := fmt.Sprintf("%s-%s", group, name)

		port, _ := strconv.Atoi(server["port"])
		_, err := pgateClient.CreateSocks5Proxy(fullName, server["ip"], port, server["username"], server["password"], lifeHour, group)
		if err != nil {
			zlog.Errorf("create SOCKS5 proxy failed: %v", err)
			continue
		}

		fmt.Printf("代理 %d/%d 创建中: %s\n", i+1, len(servers), fullName)
		fmt.Printf("  服务器: %s:%s\n", server["ip"], server["port"])
		fmt.Printf("  用户名: %s\n", server["username"])
		fmt.Printf("  密码: %s\n", server["password"])
	}
}

func promptCreateSingleSocks5Proxy() {
	// Get proxy name with suggestion
	name := os.Getenv("DEBUG_PROXY_NAME")
	if name == "" {
		suggestedName := pgateClient.SuggestName("socks5")
		fmt.Printf("建议的代理名称: %s\n", suggestedName)

		for {
			name = utils.SurveyInput("请输入 SOCKS5 代理名称，留空则使用建议名称:")
			if name == "" {
				name = suggestedName
			}

			err := pgateClient.CheckName(name)
			if err != nil {
				if strings.Contains(err.Error(), "name exist") {
					fmt.Println("代理名称已存在，请重新输入")
					continue
				}
				fmt.Printf("检查代理是否存在失败: %v\n", err)
				return
			} else {
				break
			}
		}
	}

	// Parse SOCKS5 server info
	serverInfos := parseServerInfo()
	if serverInfos == nil || len(serverInfos) == 0 {
		fmt.Println("取消创建")
		return
	}

	// For single proxy, use the first server
	serverInfo := serverInfos[0]
	if len(serverInfos) > 1 {
		fmt.Printf("检测到 %d 个服务器，将使用第一个服务器\n", len(serverInfos))
	}

	// Get life hour
	lifeHourEnv := os.Getenv("DEBUG_PROXY_LIFE_HOUR")
	lifeHour := 0
	if lifeHourEnv != "" {
		lifeHour, _ = strconv.Atoi(lifeHourEnv)
	} else {
		lifeHour = utils.SurveyInt("代理运行时长(小时), 留空则为不限制:")
	}

	// Confirm creation
	port, _ := strconv.Atoi(serverInfo["port"])
	confirmMsg := fmt.Sprintf("将使用名称 %s 创建 SOCKS5 代理，服务器 %s:%s，用户名 %s，确认吗?", name, serverInfo["ip"], serverInfo["port"], serverInfo["username"])
	if lifeHour > 0 {
		confirmMsg = fmt.Sprintf("将使用名称 %s 创建 SOCKS5 代理，服务器 %s:%s，用户名 %s，生命周期 %d 小时，确认吗?", name, serverInfo["ip"], serverInfo["port"], serverInfo["username"], lifeHour)
	}

	if os.Getenv("DEBUG_PROXY_NAME") == "" {
		if !utils.SurveyYes(confirmMsg) {
			return
		}
	}

	_, err := pgateClient.CreateSocks5Proxy(name, serverInfo["ip"], port, serverInfo["username"], serverInfo["password"], lifeHour, "")
	if err != nil {
		zlog.Errorf("create SOCKS5 proxy failed: %v", err)
		return
	}

	fmt.Println("SOCKS5 代理创建中，配置信息：")
	fmt.Println("服务器:", serverInfo["ip"])
	fmt.Println("端口:", serverInfo["port"])
	fmt.Println("用户名:", serverInfo["username"])
	fmt.Println("密码:", serverInfo["password"])
	fmt.Println("类型: SOCKS5")
}

func parseServerInfo() []map[string]string {
	for {
		serverInfo := utils.SurveyInput("请输入 SOCKS5 服务器信息 (支持多个服务器，用逗号、空格或换行分隔):")
		if serverInfo == "" {
			return nil
		}

		// Split by multiple possible separators
		var items []string

		// First try to split by newlines
		if strings.Contains(serverInfo, "\n") {
			items = strings.Split(serverInfo, "\n")
		} else if strings.Contains(serverInfo, ",") {
			// Split by comma and clean up spaces
			rawItems := strings.Split(serverInfo, ",")
			for _, item := range rawItems {
				item = strings.TrimSpace(item)
				if item != "" {
					items = append(items, item)
				}
			}
		} else {
			// Split by one or more spaces
			rawItems := strings.Fields(serverInfo)
			for _, item := range rawItems {
				item = strings.TrimSpace(item)
				if item != "" {
					items = append(items, item)
				}
			}
		}

		if len(items) == 0 {
			fmt.Println("没有找到有效的服务器信息")
			continue
		}

		var servers []map[string]string
		var invalidItems []string

		for i, item := range items {
			item = strings.TrimSpace(item)
			if item == "" {
				continue
			}

			server := parseSingleServerInfo(item)
			if server != nil {
				servers = append(servers, server)
			} else {
				invalidItems = append(invalidItems, fmt.Sprintf("项目%d: %s", i+1, item))
			}
		}

		if len(invalidItems) > 0 {
			fmt.Println("以下项目格式错误:")
			for _, invalid := range invalidItems {
				fmt.Println("  " + invalid)
			}
			fmt.Println("请使用以下格式之一:")
			fmt.Println("格式1: username:password@ip:port (例如: user123:pass456@*************:1080)")
			fmt.Println("格式2: ip:port:username:password (例如: *************:1080:user123:pass456)")
			continue
		}

		if len(servers) == 0 {
			fmt.Println("没有找到有效的服务器信息")
			continue
		}

		return servers
	}
}

func parseSingleServerInfo(serverInfo string) map[string]string {
	// Try format 1: username:password@ip:port
	if strings.Contains(serverInfo, "@") {
		parts := strings.Split(serverInfo, "@")
		if len(parts) == 2 {
			authParts := strings.Split(parts[0], ":")
			addrParts := strings.Split(parts[1], ":")
			if len(authParts) == 2 && len(addrParts) == 2 {
				if _, err := strconv.Atoi(addrParts[1]); err == nil {
					return map[string]string{
						"username": authParts[0],
						"password": authParts[1],
						"ip":       addrParts[0],
						"port":     addrParts[1],
					}
				}
			}
		}
	} else {
		// Try format 2: ip:port:username:password
		parts := strings.Split(serverInfo, ":")
		if len(parts) == 4 {
			if _, err := strconv.Atoi(parts[1]); err == nil {
				return map[string]string{
					"ip":       parts[0],
					"port":     parts[1],
					"username": parts[2],
					"password": parts[3],
				}
			}
		}
	}

	return nil
}

func promptDeleteProxies() {
	name := utils.SurveyInput("请输入要删除的代理名称(或者_GroupName):")
	if name == "" {
		fmt.Println("代理名称不能为空")
		return
	}

	if strings.HasPrefix(name, "_") {
		group := name[1:]
		confirm := utils.SurveyInput("确认删除组 " + group + " 的所有代理吗? 输入 \"delete group " + group + "\" 确认:")
		if confirm == "delete group "+group {
			for _, proxy := range activeProxies() {
				if proxy.Group == group && proxy.DeletedAt == nil {
					zlog.Debugf("deleting proxy from group: %s, proxy: %s", group, proxy.Name)
					err := pgateClient.DeleteProxy(proxy.Name)
					if err != nil {
						zlog.Errorf("delete proxy failed: %v", err)
					}
				}
			}
		} else {
			fmt.Println("取消删除")
			return
		}
	} else {
		needConfirm := true
		if strings.HasPrefix(name, "!") {
			name = name[1:]
			needConfirm = false
			if !utils.SurveyYes("确认删除代理 " + name + " 吗?") {
				return
			}
		}
		names := strings.Split(name, ",")
		for _, name := range names {
			name = strings.TrimSpace(name)
			proxy := getProxyByName(name)
			if proxy == nil {
				fmt.Printf("代理不存在: %s\n", name)
				continue
			}

			doIt := true
			if needConfirm && !utils.SurveyYes("确认删除代理 "+name+" 吗?") {
				doIt = false
			}
			if doIt {
				err := pgateClient.DeleteProxy(name)
				if err != nil {
					zlog.Errorf("delete proxy failed: %v", err)
					return
				}
				fmt.Println("代理删除中，请稍后查看")
			}
		}
	}
}

func promptIncreaseLifeHour() {
	name := utils.SurveyInput("请输入要增加生命周期的代理名称(或者_GroupName):")
	if name == "" {
		fmt.Println("代理名称不能为空")
		return
	}

	hour := utils.SurveyInt("请输入要增加的生命周期(小时), 负数则为减少:")

	if strings.HasPrefix(name, "_") {
		group := name[1:]
		confirmStr := fmt.Sprintf("%s %s %d hours", "increase", group, hour)
		if hour < 0 {
			confirmStr = fmt.Sprintf("%s %s %d hours", "decrease", group, hour)
		}
		confirm := utils.SurveyInput(fmt.Sprintf("输入 \"%s\" 确认:", confirmStr))
		if strings.EqualFold(confirm, confirmStr) {
			for _, proxy := range activeProxies() {
				if proxy.Group == group && proxy.DeletedAt == nil {
					err := pgateClient.IncreaseLifeHour(proxy.Name, hour)
					if err != nil {
						zlog.Errorf("increase life hour failed: %v", err)
						continue
					}
					msg := fmt.Sprintf("increased %d hours for %s", hour, proxy.Name)
					if hour < 0 {
						msg = fmt.Sprintf("decreased %d hours for %s", hour, proxy.Name)
					}
					zlog.Infof(msg)
				}
			}
		} else {
			fmt.Println("取消设置")
			return
		}
	} else {
		proxy := getProxyByName(name)
		if proxy == nil {
			fmt.Println("代理不存在")
			return
		}

		if utils.SurveyYes("确认增加代理 " + name + " 的生命周期 " + strconv.Itoa(hour) + " 小时吗?") {
			err := pgateClient.IncreaseLifeHour(name, hour)
			if err != nil {
				zlog.Errorf("set life hour failed: %v", err)
				return
			}
			fmt.Println("代理生命周期增加成功")
		}
	}
}

func promptRepairProxy() {
	name := utils.SurveyInput("请输入要修复的代理名称:")
	if name == "" {
		fmt.Println("代理名称不能为空")
		return
	}

	p := getProxyByName(name)
	if p == nil {
		fmt.Println("代理不存在")
		return
	}

	if p.Status != pgate.ProxyStatusFail {
		fmt.Println("仅支持修复状态为失败的代理")
		return
	}

	if utils.SurveyYes("确认修复代理 " + name + " 吗?") {
		err := pgateClient.RepairProxy(name)
		if err != nil {
			zlog.Errorf("fix proxy failed: %v", err)
			return
		}

		fmt.Println("代理修复中，请稍后查看状态")
	}
}

func promptStopProxy() {
	name := utils.SurveyInput("请输入要停止的代理名称(或者_GroupName):")
	if name == "" {
		fmt.Println("代理名称不能为空")
		return
	}

	if strings.HasPrefix(name, "_") {
		group := name[1:]
		confirm := utils.SurveyInput("确认停止组 " + group + " 的所有代理吗? 输入 \"stop group " + group + "\" 确认:")
		if confirm == "stop group "+group {
			for _, proxy := range activeProxies() {
				if proxy.Group == group {
					err := pgateClient.StopProxy(proxy.Name)
					if err != nil {
						zlog.Errorf("stop proxy failed: %v", err)
					}
				}
			}
			fmt.Println("组内代理停止中，请稍后查看状态")
		} else {
			fmt.Println("取消停止")
			return
		}
	} else {
		needConfirm := true
		if strings.HasPrefix(name, "!") {
			name = name[1:]
			needConfirm = false
			if !utils.SurveyYes("确认停止代理 " + name + " 吗?") {
				return
			}
		}

		names := strings.Split(name, ",")
		for _, name := range names {
			name = strings.TrimSpace(name)

			p := getProxyByName(name)
			if p == nil {
				fmt.Println("代理 " + name + " 不存在")
				continue
			}

			if needConfirm && !utils.SurveyYes("确认停止代理 "+name+" 吗?") {
				continue
			}

			err := pgateClient.StopProxy(name)
			if err != nil {
				zlog.Errorf("stop proxy failed: %v", err)
				continue
			}
			fmt.Printf("代理 %s 停止中，请稍后查看状态\n", name)
		}
	}
}

func promptRemoveLogForProgram() {
	name := utils.SurveyInput("请输入要删除日志的代理名称:")
	if name == "" {
		fmt.Println("代理名称不能为空")
		return
	}

	if strings.HasPrefix(name, "_") {
		group := name[1:]
		confirm := utils.SurveyInput("确认清理组 " + group + " 的所有代理的日志吗? 输入 \"removeLog group " + group + "\" 确认:")
		if confirm == "removeLog group "+group {
			for _, proxy := range activeProxies() {
				if proxy.Group == group {
					err := pgateClient.RemoveLogForProgram(proxy.Name)
					if err != nil {
						zlog.Errorf("remove log for proxy failed, name: %s, err: %v", proxy.Name, err)
					}
				}
			}
			fmt.Println("组内代理的日志清理中，请稍后查看状态")
		} else {
			fmt.Println("取消清理")
			return
		}
	} else {
		needConfirm := true
		if strings.HasPrefix(name, "!") {
			name = name[1:]
			needConfirm = false
			if !utils.SurveyYes("确认清理代理 " + name + " 的日志吗?") {
				return
			}
		}

		names := strings.Split(name, ",")
		for _, name := range names {
			name = strings.TrimSpace(name)

			p := getProxyByName(name)
			if p == nil {
				fmt.Println("代理 " + name + " 不存在")
				continue
			}

			if needConfirm && !utils.SurveyYes("确认清理代理 "+name+" 的日志吗?") {
				continue
			}

			err := pgateClient.RemoveLogForProgram(name)
			if err != nil {
				zlog.Errorf("remove log for program failed: %v", err)
				continue
			}
			fmt.Printf("代理 %s 的日志清理中，请稍后查看状态\n", name)
		}
	}
}

func promptStartProxy() {
	name := utils.SurveyInput("请输入要启动的代理名称(或者_GroupName):")
	if name == "" {
		fmt.Println("代理名称不能为空")
		return
	}

	if strings.HasPrefix(name, "_") {
		group := name[1:]
		confirm := utils.SurveyInput("确认启动组 " + group + " 的所有代理吗? 输入 \"start group " + group + "\" 确认:")
		if confirm == "start group "+group {
			for _, proxy := range activeProxies() {
				if proxy.Group == group {
					err := pgateClient.StartProxy(proxy.Name)
					if err != nil {
						zlog.Errorf("start proxy failed: %v", err)
					}
				}
			}
			fmt.Println("组内代理启动中，请稍后查看状态")
		} else {
			fmt.Println("取消启动")
			return
		}
	} else {
		needConfirm := true
		if strings.HasPrefix(name, "!") {
			name = name[1:]
			needConfirm = false
			if !utils.SurveyYes("确认启动代理 " + name + " 吗?") {
				return
			}
		}

		names := strings.Split(name, ",")
		for _, name := range names {
			name = strings.TrimSpace(name)

			p := getProxyByName(name)
			if p == nil {
				fmt.Println("代理 " + name + " 不存在")
				continue
			}

			if needConfirm && !utils.SurveyYes("确认启动代理 "+name+" 吗?") {
				continue
			}

			err := pgateClient.StartProxy(name)
			if err != nil {
				zlog.Errorf("start proxy failed: %v", err)
				continue
			}
			fmt.Printf("代理 %s 启动中，请稍后查看状态\n", name)
		}
	}
}

func testAllProxies() {
	fmt.Println("代理测试中...")
	pgateClient.TestAllProxies()
	err := refreshProxiesAndTemplates()
	if err != nil {
		zlog.Errorf("refresh proxies and templates failed: %v", err)
	}
	fmt.Println(formatProxies())
}

func formatProxies() string {
	proxies := activeProxies()
	if len(proxies) == 0 {
		return "没有代理"
	}
	return pgate.FormatProxies(proxies, true)
}

func formatDeletedProxies() string {
	proxies := deletedProxies()
	if len(proxies) == 0 {
		return "没有已删除代理"
	}
	return pgate.FormatProxies(proxies, true)
}

func formatLoginForwarders() string {
	loginBooks, err := pgateClient.ProxyLogins()
	if err != nil {
		return fmt.Sprintf("get forwarders failed: %v", err)
	}
	return pgate.FormatLoginForwarders(loginBooks, true)
}

func formatForwarders() string {
	loginBooks, err := pgateClient.ProxyLogins()
	if err != nil {
		return fmt.Sprintf("get forwarders failed: %v", err)
	}
	return pgate.FormatForwarders(loginBooks, true)
}

func formatGroupForwarders() string {
	groupForwarders, err := pgateClient.GroupForwarders()
	if err != nil {
		return fmt.Sprintf("get forwarders failed: %v", err)
	}
	return pgate.FormatGroupForwarders(groupForwarders, true)
}

func promptPrintReservedInstances() {
	choices := []string{
		"1. 打印 AWS 预留实例",
		"2. 打印 AWS 预留实例覆盖情况",
		"3. 打印 Google 承诺使用折扣",
		"4. 打印 Google 承诺使用折扣覆盖情况",
		"Q. 退出",
	}
	choice, _ := utils.SurveySelect("请选择操作:", choices)

	switch choice {
	case 0:
		printAWSReservedInstances()
	case 1:
		printAWSReservationCoverage()
	case 2:
		printGoogleCommittedUsageDiscounts()
	case 3:
		printGoogleCommittedUsageDiscountsCoverage()
	}
}

func printAWSReservedInstances() {
	instanceMap, err := pgateClient.GetAWSReservedInstances()
	if err != nil {
		fmt.Printf("get reserved instances failed: %v\n", err)
		return
	}

	for account, instances := range instanceMap {
		fmt.Printf("[Account - %s]\n", account)
		fmt.Println(pgate.FormatAWSReservedInstances(instances))
	}
}

func printAWSReservationCoverage() {
	days := utils.SurveyInt("请输入查询天数，留空则为 30 天:")
	if days <= 0 {
		days = 30
	}
	coverageMap, err := pgateClient.GetAWSReservationCoverage(days)
	if err != nil {
		fmt.Printf("get reservation coverage failed: %v\n", err)
		return
	}

	for account, coverages := range coverageMap {
		fmt.Printf("[Account - %s]\n", account)
		fmt.Println(pgate.FormatAWSReservationCoverage(coverages))
	}
}

func printGoogleCommittedUsageDiscounts() {
	commitsMap, err := pgateClient.GetGoogleCommitments()
	if err != nil {
		fmt.Printf("get google commitments failed: %v\n", err)
		return
	}

	for account, commits := range commitsMap {
		fmt.Printf("[Account - %s]\n", account)
		fmt.Println(pgate.FormatGoogleCommittedUsageDiscounts(commits))
	}
}

func printGoogleCommittedUsageDiscountsCoverage() {
	coverageMap, err := pgateClient.GetGoogleCommitmentsConverage()
	if err != nil {
		fmt.Printf("get google commitments coverage failed: %v\n", err)
		return
	}

	for account, coverages := range coverageMap {
		fmt.Printf("[Account - %s]\n", account)
		fmt.Println(pgate.FormatGoogleCommittedUsageDiscountsCoverage(coverages))
	}
}

func promptSyncCloudStorage() {
	pgateID := utils.SurveyInput("请输入 PgateID, 留空则使用系统配置:")
	data, err := pgateClient.CheckSyncProxies(pgateID)
	if err != nil {
		fmt.Printf("check sync proxies failed: %v\n", err)
		return
	}

	fmt.Printf("云存储文件:\n%s\n", data["file"])
	fmt.Printf("文件对比:\n%s\n", data["diff"])

	if !utils.SurveyYes("确认同步吗?") {
		return
	}

	err = pgateClient.SyncProxies(pgateID)
	if err != nil {
		fmt.Printf("同步云存储代理失败: %v\n", err)
	} else {
		fmt.Println("同步云存储代理成功")
	}
}

func promptClearAccountsForProxy() {
	choices := []string{"*"}
	for _, proxy := range activeProxies() {
		choices = append(choices, proxy.Name)
	}
	_, proxyName := utils.SurveySelect("请选择要清除账户的代理（* 表示所有代理）:", choices)
	if proxyName == "" {
		fmt.Println("代理名称不能为空")
		return
	}

	err := pgateClient.ClearAccountsForProxy(proxyName)
	if err != nil {
		fmt.Printf("清除代理 %s 的账户失败: %v\n", proxyName, err)
	} else {
		fmt.Printf("清除代理 %s 的账户成功\n", proxyName)
	}
}

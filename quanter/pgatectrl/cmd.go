package pgatectrl

import (
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path"
	"slices"
	"strings"

	"github.com/google/go-cmp/cmp"
	"github.com/spf13/cast"
	"github.com/wizhodl/pgate"
	"github.com/wizhodl/quanter/common/command"
)

type ProxyCommand struct {
	command.Command
	controller *ProxyManagerController
	manager    *pgate.ProxyManager
}

func (this *ProxyCommand) PrePrepare() bool {
	if this.controller.ProxyManager == nil {
		this.ErrorMsgf("proxy manager not started")
		return false
	}
	this.manager = this.controller.ProxyManager
	return true
}

type NewProxyCommand struct {
	ProxyCommand
}

func NewNewProxyCommand(controller *ProxyManagerController) *NewProxyCommand {
	return &NewProxyCommand{
		ProxyCommand: ProxyCommand{
			Command: command.Command{
				Name:            "newProxy",
				Alias:           []string{"n"},
				Instruction:     "`.newProxy kr/jp/hk lifeHour AuthCode` 新增代理",
				RequiresConfirm: false,
				ArgMin:          2,
				ArgMax:          3,
				AuthcodePos:     3,
			},
			controller: controller,
		},
	}
}

func (this *NewProxyCommand) Do() bool {
	templateName := this.Args[0]
	validTemplates := this.manager.GetTemplateNames()
	if !slices.Contains(validTemplates, templateName) {
		this.ErrorMsgf("template not found: , valid templates: %s", templateName, strings.Join(validTemplates, "/"))
		return false
	}
	lifeHour := cast.ToInt(this.Args[1])
	name := this.manager.SuggestName(templateName)

	proxy, err := this.controller.ProxyManager.CreateProxy(name, templateName, lifeHour, "")
	if err != nil {
		this.ErrorMsgf("创建代理失败: %v", err)
		return false
	}
	this.SendMsgf("代理创建中，配置如下：\n\n```域名: %s\n端口: 443\n密码: %s```\n", proxy.GetDomain(), proxy.Password)

	return true
}

type ListProxyCommand struct {
	ProxyCommand
}

func NewListProxyCommand(controller *ProxyManagerController) *ListProxyCommand {
	return &ListProxyCommand{
		ProxyCommand: ProxyCommand{
			Command: command.Command{
				Name:            "listProxy",
				Alias:           []string{"l"},
				Instruction:     "`.listProxy ` 打印代理列表",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          0,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
}

func (this *ListProxyCommand) Do() bool {
	this.SendMsgf("代理列表: \n\n```%s```\n", this.manager.PrintProxies(false))
	return true
}

type ListLoginForwardersCommand struct {
	ProxyCommand
}

func NewListLoginForwardersCommand(controller *ProxyManagerController) *ListLoginForwardersCommand {
	return &ListLoginForwardersCommand{
		ProxyCommand: ProxyCommand{
			Command: command.Command{
				Name:            "listLoginForwarders",
				Alias:           []string{"llf"},
				Instruction:     "`.listLoginForwarders ` 打印 forwarder 登录列表",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          0,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
}

func (this *ListLoginForwardersCommand) Do() bool {
	this.SendMsgf("LoginForwarders 列表: \n\n```%s```\n", this.manager.FormatLoginForwarders(false))
	return true
}

type ListGroupForwardersCommand struct {
	ProxyCommand
}

func NewListGroupForwardersCommand(controller *ProxyManagerController) *ListGroupForwardersCommand {
	return &ListGroupForwardersCommand{
		ProxyCommand: ProxyCommand{
			Command: command.Command{
				Name:            "listGroupForwarders",
				Alias:           []string{"lgf"},
				Instruction:     "`.listGroupForwarders ` 打印 group forwarder 列表",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          0,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
}

func (this *ListGroupForwardersCommand) Do() bool {
	this.SendMsgf("Group Forwarders 列表: \n\n```%s```\n", this.manager.FormatGroupForwarders(false))
	return true
}

type ListForwardersCommand struct {
	ProxyCommand
}

func NewListForwardersCommand(controller *ProxyManagerController) *ListForwardersCommand {
	return &ListForwardersCommand{
		ProxyCommand: ProxyCommand{
			Command: command.Command{
				Name:            "listForwarders",
				Alias:           []string{"lf"},
				Instruction:     "`.listForwarders ` 打印 forwarder 列表",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          0,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
}

func (this *ListForwardersCommand) Do() bool {
	this.SendMsgf("Forwarders 列表: \n\n```%s```\n", this.manager.FormatForwarders(false))
	return true
}

type ListDeletedProxyCommand struct {
	ProxyCommand
}

func NewListDeletedProxyCommand(controller *ProxyManagerController) *ListDeletedProxyCommand {
	return &ListDeletedProxyCommand{
		ProxyCommand: ProxyCommand{
			Command: command.Command{
				Name:            "listDeletedProxy",
				Alias:           []string{"ld"},
				Instruction:     "`.listDeletedProxy ` 打印已删除代理列表",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          0,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
}

func (this *ListDeletedProxyCommand) Do() bool {
	this.SendMsgf("已删除代理列表: \n\n```%s```\n", this.manager.PrintDeletedProxies(false))
	return true
}

type TestProxyCommand struct {
	ProxyCommand
}

func NewTestProxyCommand(controller *ProxyManagerController) *TestProxyCommand {
	return &TestProxyCommand{
		ProxyCommand: ProxyCommand{
			Command: command.Command{
				Name:            "testProxy",
				Alias:           []string{"t"},
				Instruction:     "`.testProxy ` 测试全部代理",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          0,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
}

func (this *TestProxyCommand) Do() bool {
	this.SendMsgf("代理测试中...")
	this.manager.TestAllProxies()
	this.SendMsgf("代理列表: \n\n```%s```\n", this.manager.PrintProxies(false))
	return true
}

type RestartProgramCommand struct {
	ProxyCommand
}

func NewRestartProgramCommand(controller *ProxyManagerController) *RestartProgramCommand {
	return &RestartProgramCommand{
		ProxyCommand: ProxyCommand{
			Command: command.Command{
				Name:            "restartProgram",
				Instruction:     "`.restartProgram name1,name2,_group1` 重启服务器上的代理程序（非服务器实例）",
				RequiresConfirm: false,
				ArgMin:          1,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
}

func (this *RestartProgramCommand) Do() bool {
	this.SendMsgf("重启程序中...")
	name := this.Args[0]
	proxies := this.manager.GetProxiesByName(name)

	for _, proxy := range proxies {
		if proxy.Status == pgate.ProxyStatusOK || proxy.Status == pgate.ProxyStatusFail {
			err := proxy.RestartTrojan()
			if err != nil {
				this.ErrorMsgf("重启代理程序失败: %s, error: %v", proxy.Name, err)
				continue
			}
			this.SendMsgf("重启程序成功 %s", proxy.Name)
		} else {
			this.ErrorMsgf("代理状态异常: %s, status: %s，取消重启", proxy.Name, proxy.Status)
		}
	}
	return true
}

type RemoveLogForProgramCommand struct {
	ProxyCommand
}

func NewRemoveLogForProgramCommand(controller *ProxyManagerController) *RemoveLogForProgramCommand {
	return &RemoveLogForProgramCommand{
		ProxyCommand: ProxyCommand{
			Command: command.Command{
				Name:            "removeLogForProgram",
				Instruction:     "`.removeLogForProgram name1,name2,_group1` 删除代理程序的日志",
				RequiresConfirm: false,
				ArgMin:          1,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
}

func (this *RemoveLogForProgramCommand) Do() bool {
	this.SendMsgf("删除日志中...")
	name := this.Args[0]
	proxies := this.manager.GetProxiesByName(name)
	for _, proxy := range proxies {
		proxy.RemoveLogForTrojan()
	}
	return true
}

type DeleteProxyCommand struct {
	ProxyCommand
}

func NewDeleteProxyCommand(controller *ProxyManagerController) *DeleteProxyCommand {
	return &DeleteProxyCommand{
		ProxyCommand: ProxyCommand{
			Command: command.Command{
				Name:            "deleteProxy",
				Alias:           []string{"d"},
				Instruction:     "`.deleteProxy name AuthCode` 删除代理",
				RequiresConfirm: false,
				ArgMin:          1,
				ArgMax:          2,
				AuthcodePos:     2,
			},
			controller: controller,
		},
	}
}

func (this *DeleteProxyCommand) Do() bool {
	err := this.manager.DeleteProxy(this.Args[0])
	if err != nil {
		this.ErrorMsgf("delete proxy failed: %s", err)
		return false
	}
	this.SendMsgf("代理删除中，请稍后查看")
	return true
}

type RepairProxyCommand struct {
	ProxyCommand
}

func NewRepairProxyCommand(controller *ProxyManagerController) *RepairProxyCommand {
	return &RepairProxyCommand{
		ProxyCommand: ProxyCommand{
			Command: command.Command{
				Name:            "repairProxy",
				Alias:           []string{"r"},
				Instruction:     "`.repairProxy name AuthCode` 修复代理",
				RequiresConfirm: false,
				ArgMin:          1,
				ArgMax:          2,
				AuthcodePos:     2,
			},
			controller: controller,
		},
	}
}

func (this *RepairProxyCommand) Do() bool {
	err := this.manager.RepairProxy(this.Args[0])
	if err != nil {
		this.ErrorMsgf("fix proxy failed: %s", err)
		return false
	}
	this.SendMsgf("代理修复中，请稍后查看状态")
	return true
}

type StopProxyCommand struct {
	ProxyCommand
}

func NewStopProxyCommand(controller *ProxyManagerController) *StopProxyCommand {
	return &StopProxyCommand{
		ProxyCommand: ProxyCommand{
			Command: command.Command{
				Name:            "stopProxy",
				Alias:           []string{"stop"},
				Instruction:     "`.stopProxy name AuthCode` 停止代理",
				RequiresConfirm: false,
				ArgMin:          1,
				ArgMax:          2,
				AuthcodePos:     2,
			},
			controller: controller,
		},
	}
}

func (this *StopProxyCommand) Do() bool {
	err := this.manager.StopProxy(this.Args[0])
	if err != nil {
		this.ErrorMsgf("stop proxy failed: %s", err)
		return false
	}
	this.SendMsgf("代理停止中，请稍后查看状态")
	return true
}

type StartProxyCommand struct {
	ProxyCommand
}

func NewStartProxyCommand(controller *ProxyManagerController) *StartProxyCommand {
	return &StartProxyCommand{
		ProxyCommand: ProxyCommand{
			Command: command.Command{
				Name:            "startProxy",
				Alias:           []string{"start"},
				Instruction:     "`.startProxy name AuthCode` 启动代理",
				RequiresConfirm: false,
				ArgMin:          1,
				ArgMax:          2,
				AuthcodePos:     2,
			},
			controller: controller,
		},
	}
}

func (this *StartProxyCommand) Do() bool {
	err := this.manager.StartProxy(this.Args[0])
	if err != nil {
		this.ErrorMsgf("start proxy failed: %s", err)
		return false
	}
	this.SendMsgf("代理启动中，请稍后查看状态")
	return true
}

type EnableWARPCmd struct {
	ProxyCommand
}

func NewEnableWARPCmd(controller *ProxyManagerController) *EnableWARPCmd {
	return &EnableWARPCmd{
		ProxyCommand: ProxyCommand{
			Command: command.Command{
				Name:            "enableWARP",
				Alias:           []string{"ew"},
				Instruction:     "`.enableWARP name AuthCode` 启用 WARP",
				RequiresConfirm: false,
				ArgMin:          1,
				ArgMax:          2,
				AuthcodePos:     2,
			},
			controller: controller,
		},
	}
}

func (this *EnableWARPCmd) Do() bool {
	name := this.Args[0]
	p := this.manager.GetProxyByName(name)
	if p == nil {
		this.ErrorMsgf("proxy not found: %s", name)
		return false
	}

	err := p.EnableWARP()
	if err != nil {
		this.ErrorMsgf("enable proxy warp failed: %s", err)
		return false
	}
	this.manager.Save()
	this.SendMsgf("代理启用 WARP 成功")
	return true
}

type DisableWARPCmd struct {
	ProxyCommand
}

func NewDisableWARPCmd(controller *ProxyManagerController) *DisableWARPCmd {
	return &DisableWARPCmd{
		ProxyCommand: ProxyCommand{
			Command: command.Command{
				Name:            "disableWARP",
				Alias:           []string{"dw"},
				Instruction:     "`.disableWARP name AuthCode` 禁用 WARP",
				RequiresConfirm: false,
				ArgMin:          1,
				ArgMax:          2,
				AuthcodePos:     2,
			},
			controller: controller,
		},
	}
}

func (this *DisableWARPCmd) Do() bool {
	name := this.Args[0]
	p := this.manager.GetProxyByName(name)
	if p == nil {
		this.ErrorMsgf("proxy not found: %s", name)
		return false
	}

	err := p.DisableWARP()
	if err != nil {
		this.ErrorMsgf("disable proxy warp failed: %s", err)
		return false
	}
	this.manager.Save()
	this.SendMsgf("代理禁用 WARP 成功")
	return true
}

type IncreaseLifeHourCmd struct {
	ProxyCommand
}

func NewIncreaseLifeHourCmd(controller *ProxyManagerController) *IncreaseLifeHourCmd {
	return &IncreaseLifeHourCmd{
		ProxyCommand: ProxyCommand{
			Command: command.Command{
				Name:            "increaseLifeHour",
				Alias:           []string{"il"},
				Instruction:     "`.increaseLifeHour name hour AuthCode` 增加代理生命周期，负数为减少",
				RequiresConfirm: false,
				ArgMin:          2,
				ArgMax:          3,
				AuthcodePos:     3,
			},
			controller: controller,
		},
	}
}

func (this *IncreaseLifeHourCmd) Do() bool {
	name := this.Args[0]
	lifeHour := cast.ToInt(this.Args[1])
	if strings.HasPrefix(name, "_") {
		group := name[1:]
		for _, proxy := range this.manager.Proxies {
			if proxy.Group == group && proxy.DeletedAt == nil {
				err := this.manager.IncreaseLifeHour(proxy.Name, lifeHour)
				if err != nil {
					this.ErrorMsgf("increase life hour failed: %v", err)
					continue
				}
				msg := fmt.Sprintf("increased %d hours for %s", lifeHour, proxy.Name)
				if lifeHour < 0 {
					msg = fmt.Sprintf("decreased %d hours for %s", lifeHour, proxy.Name)
				}
				this.SendMsgf(msg)
			}
		}
	} else {
		err := this.manager.IncreaseLifeHour(name, lifeHour)
		if err != nil {
			this.ErrorMsgf("increase life hour failed: %s", err)
			return false
		}
	}

	this.SendMsgf("代理生命周期设置成功")
	return true
}

type ListAWSReservedInstancesCommand struct {
	ProxyCommand
}

func NewListAWSReservedInstancesCommand(controller *ProxyManagerController) *ListAWSReservedInstancesCommand {
	return &ListAWSReservedInstancesCommand{
		ProxyCommand: ProxyCommand{
			Command: command.Command{
				Name:            "listAWSReservedInstances",
				Alias:           []string{"lari"},
				Instruction:     "`.listAWSReservedInstances ` 打印 AWS 预留实例",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          0,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
}

func (this *ListAWSReservedInstancesCommand) Prepare() bool {
	this.SendMsgf("查询中，请稍后...")
	return true
}

func (this *ListAWSReservedInstancesCommand) Do() bool {
	awsReservedInstances, err := this.manager.GetAWSReservedInstances()
	if err != nil {
		this.ErrorMsgf("get aws reserved instances failed: %s", err)
		return false
	}

	for account, instances := range awsReservedInstances {
		this.SendMsgf("\n[AWS Account - %s]\n```%s```", account, pgate.FormatAWSReservedInstances(instances))
	}

	return true
}

type ListAWSReservationCoveragesCommand struct {
	ProxyCommand
}

func NewListAWSReservationCoveragesCommand(controller *ProxyManagerController) *ListAWSReservationCoveragesCommand {
	return &ListAWSReservationCoveragesCommand{
		ProxyCommand: ProxyCommand{
			Command: command.Command{
				Name:            "listAWSReservationCoverages",
				Alias:           []string{"larc"},
				Instruction:     "`.listAWSReservationCoverages [days]` 打印 AWS 预留实例覆盖情况，默认 30 天",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
}

func (this *ListAWSReservationCoveragesCommand) Prepare() bool {
	this.SendMsgf("查询中，请稍后...")
	return true
}

func (this *ListAWSReservationCoveragesCommand) Do() bool {
	var days int
	if len(this.Args) > 0 {
		days = cast.ToInt(this.Args[0])
	}

	coverages, err := this.manager.GetAWSReservationCoverages(days)
	if err != nil {
		this.ErrorMsgf("get aws reservation coverages failed: %s", err)
		return false
	}

	for account, c := range coverages {
		this.SendMsgf("\n[AWS Account - %s]\n```%s```", account, pgate.FormatAWSReservationCoverage(c))
	}

	return true
}

type ListGoogelCommitmentsCommand struct {
	ProxyCommand
}

func NewListGoogelCommitmentsCommand(controller *ProxyManagerController) *ListGoogelCommitmentsCommand {
	return &ListGoogelCommitmentsCommand{
		ProxyCommand: ProxyCommand{
			Command: command.Command{
				Name:            "listGoogelCommitments",
				Alias:           []string{"lgc"},
				Instruction:     "`.listGoogelCommitments ` 打印 Google 承诺使用折扣",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          0,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
}

func (this *ListGoogelCommitmentsCommand) Prepare() bool {
	this.SendMsgf("查询中，请稍后...")
	return true
}

func (this *ListGoogelCommitmentsCommand) Do() bool {
	commits, err := this.manager.GetGoogleCommitments()
	if err != nil {
		this.ErrorMsgf("get google commitments failed: %s", err)
		return false
	}

	for account, c := range commits {
		this.SendMsgf("\n[Google Account - %s]\n```%s```", account, pgate.FormatGoogleCommittedUsageDiscounts(c))
	}

	return true
}

type ListGoogelCommitmentsCoveragesCommand struct {
	ProxyCommand
}

func NewListGoogelCommitmentsCoveragesCommand(controller *ProxyManagerController) *ListGoogelCommitmentsCoveragesCommand {
	return &ListGoogelCommitmentsCoveragesCommand{
		ProxyCommand: ProxyCommand{
			Command: command.Command{
				Name:            "listGoogelCommitmentsCoverages",
				Alias:           []string{"lgcc"},
				Instruction:     "`.listGoogelCommitmentsCoverages` 打印 Google 承诺使用折扣覆盖率",
				RequiresConfirm: false,
				ArgMin:          0,
				ArgMax:          0,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
}

func (this *ListGoogelCommitmentsCoveragesCommand) Prepare() bool {
	this.SendMsgf("查询中，请稍后...")
	return true
}

func (this *ListGoogelCommitmentsCoveragesCommand) Do() bool {
	coverages, err := this.manager.GetGoogleCommitmentCoverages()
	if err != nil {
		this.ErrorMsgf("get google commitments coverage failed: %s", err)
		return false
	}

	for account, c := range coverages {
		this.SendMsgf("\n[Google Account - %s]\n```%s```", account, pgate.FormatGoogleCommittedUsageDiscountsCoverage(c))
	}

	return true
}

type SyncCloudStorageCommand struct {
	ProxyCommand
	cloudStorage []byte
}

func NewSyncCloudStorageCommand(controller *ProxyManagerController) *SyncCloudStorageCommand {
	return &SyncCloudStorageCommand{
		ProxyCommand: ProxyCommand{
			Command: command.Command{
				Name:            "syncCloudStorage",
				Alias:           []string{"sync"},
				Instruction:     "`.syncCloudStorage [pgate_id]` 同步 pgate 云存储代理, 默认使用配置中的 ID",
				RequiresConfirm: true,
				ArgMin:          0,
				ArgMax:          1,
				AuthcodePos:     -1,
			},
			controller: controller,
		},
	}
}

func (this *SyncCloudStorageCommand) Prepare() bool {
	if this.manager.Options.SyncStorage == nil || this.manager.Options.SyncStorage.Account == nil {
		this.ErrorMsgf("未配置 pgate 云存储")
		return false
	}
	fromPgateID := this.manager.Options.SyncStorage.FromPgateID
	if len(this.Args) > 0 {
		fromPgateID = this.Args[0]
	}
	if fromPgateID == "" {
		this.ErrorMsgf("未指定 pgate ID")
		return false
	}

	key := this.manager.GetSyncStorageKey(fromPgateID)
	cloudFile, err := this.manager.Options.SyncStorage.Account.DownloadFileFromS3(
		this.manager.Options.SyncStorage.Region,
		this.manager.Options.SyncStorage.Bucket,
		key,
	)
	if err != nil {
		this.ErrorMsgf("下载文件失败: %s", err)
		return false
	}

	// 验证格式
	err = json.Unmarshal(cloudFile, &pgate.ProxyManager{})
	if err != nil {
		this.ErrorMsgf("文件格式错误: %s", err)
		return false
	}

	this.SendFileMessage(fmt.Sprintf("文件确认: %s", key), string(cloudFile), "")

	// 对比本地存储
	localFilePath := path.Join(this.manager.Options.DataDir, pgate.STORAGE_FILE)
	localFile, err := os.Open(localFilePath)
	if err != nil {
		this.ErrorMsgf("打开本地文件失败: %s", err)
		return false
	}
	defer localFile.Close()
	localFileContent, err := io.ReadAll(localFile)
	if err != nil {
		this.ErrorMsgf("读取本地文件失败: %s", err)
		return false
	}

	diff := cmp.Diff(string(localFileContent), string(cloudFile))
	if diff == "" {
		this.SendMsgf("两个文件完全一致, 无需更新")
		return false
	} else {
		this.SendFileMessage("本地与云端文件对比", diff, "")
	}

	this.cloudStorage = cloudFile
	return true
}

func (this *SyncCloudStorageCommand) Do() bool {
	tmpManager := &pgate.ProxyManager{}
	err := json.Unmarshal(this.cloudStorage, tmpManager)
	if err != nil {
		this.ErrorMsgf("解析文件失败: %s", err)
		return false
	}
	// 仅同步代理，不同步 forwarder，这样不影响使用中的 forwarder
	this.manager.SetProxies(tmpManager.Proxies)
	if this.manager.Gateway != nil {
		this.manager.Gateway.RefreshProxies()
	}
	this.SendMsgf("加载完成")
	return true
}
